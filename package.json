{"name": "data-sync", "type": "module", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "db:sync": "npx drizzle-kit generate && npx drizzle-kit push", "lint": "biome check .", "lint:fix": "biome check . --write"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.3", "drizzle-zod": "^0.8.2", "hono": "^4.8.5", "zod": "^4.0.5"}, "devDependencies": {"@biomejs/biome": "2.1.2", "drizzle-kit": "^0.31.4", "typescript": "^5.8.3", "wrangler": "^4.4.0"}}