/**
 * Monitoring and health check service
 * Provides system health monitoring, metrics collection, and alerting
 */

import { logger } from "@/utils";
import { dbSchema, getDb } from "@database";
import { APContactRequest } from "@libAP/requests/contactRequest";
import { CCPatientRequest } from "@libCC/request/patientRequest";

export interface HealthCheckResult {
	service: string;
	status: "healthy" | "degraded" | "unhealthy";
	responseTime: number;
	error?: string;
	details?: Record<string, any>;
}

export interface SystemHealth {
	overall: "healthy" | "degraded" | "unhealthy";
	timestamp: string;
	checks: HealthCheckResult[];
	metrics: {
		uptime: number;
		memoryUsage: NodeJS.MemoryUsage;
		errorRate: number;
		syncStats: {
			patientsToday: number;
			appointmentsToday: number;
			errorsToday: number;
		};
	};
}

export class MonitoringService {
	private db = getDb();
	private apContactRequest = new APContactRequest();
	private ccPatientRequest = new CCPatientRequest();
	private startTime = Date.now();

	/**
	 * Perform comprehensive health check
	 * @returns System health status
	 */
	async getSystemHealth(): Promise<SystemHealth> {
		const checks: HealthCheckResult[] = [];

		// Run all health checks in parallel
		const [
			databaseCheck,
			apApiCheck,
			ccApiCheck,
			memoryCheck,
		] = await Promise.allSettled([
			this.checkDatabase(),
			this.checkAPAPI(),
			this.checkCCAPI(),
			this.checkMemoryUsage(),
		]);

		// Collect results
		if (databaseCheck.status === "fulfilled") {
			checks.push(databaseCheck.value);
		} else {
			checks.push({
				service: "database",
				status: "unhealthy",
				responseTime: 0,
				error: databaseCheck.reason?.message || "Unknown error",
			});
		}

		if (apApiCheck.status === "fulfilled") {
			checks.push(apApiCheck.value);
		} else {
			checks.push({
				service: "autopatient_api",
				status: "unhealthy",
				responseTime: 0,
				error: apApiCheck.reason?.message || "Unknown error",
			});
		}

		if (ccApiCheck.status === "fulfilled") {
			checks.push(ccApiCheck.value);
		} else {
			checks.push({
				service: "clinicore_api",
				status: "unhealthy",
				responseTime: 0,
				error: ccApiCheck.reason?.message || "Unknown error",
			});
		}

		if (memoryCheck.status === "fulfilled") {
			checks.push(memoryCheck.value);
		} else {
			checks.push({
				service: "memory",
				status: "unhealthy",
				responseTime: 0,
				error: memoryCheck.reason?.message || "Unknown error",
			});
		}

		// Calculate overall status
		const overall = this.calculateOverallHealth(checks);

		// Get metrics
		const metrics = await this.getMetrics();

		return {
			overall,
			timestamp: new Date().toISOString(),
			checks,
			metrics,
		};
	}

	/**
	 * Check database connectivity and performance
	 */
	private async checkDatabase(): Promise<HealthCheckResult> {
		const start = Date.now();
		
		try {
			// Simple query to test database connectivity
			await this.db.query.patient.findFirst({
				limit: 1,
			});

			const responseTime = Date.now() - start;

			return {
				service: "database",
				status: responseTime < 1000 ? "healthy" : "degraded",
				responseTime,
				details: {
					query: "SELECT 1 FROM patients LIMIT 1",
				},
			};
		} catch (error) {
			return {
				service: "database",
				status: "unhealthy",
				responseTime: Date.now() - start,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}

	/**
	 * Check AutoPatient API connectivity
	 */
	private async checkAPAPI(): Promise<HealthCheckResult> {
		const start = Date.now();
		
		try {
			// Try to fetch a small amount of data from AP API
			await this.apContactRequest.searchContacts("healthcheck", 1);

			const responseTime = Date.now() - start;

			return {
				service: "autopatient_api",
				status: responseTime < 3000 ? "healthy" : "degraded",
				responseTime,
				details: {
					endpoint: "/contacts/search",
				},
			};
		} catch (error) {
			const responseTime = Date.now() - start;
			const statusCode = (error as any)?.statusCode || (error as any)?.status;

			// 404 is expected for health check search, so treat as healthy
			if (statusCode === 404) {
				return {
					service: "autopatient_api",
					status: "healthy",
					responseTime,
					details: {
						endpoint: "/contacts/search",
						note: "404 expected for health check",
					},
				};
			}

			return {
				service: "autopatient_api",
				status: "unhealthy",
				responseTime,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}

	/**
	 * Check CliniCore API connectivity
	 */
	private async checkCCAPI(): Promise<HealthCheckResult> {
		const start = Date.now();
		
		try {
			// Try to search for a patient (should return empty result)
			await this.ccPatientRequest.search("healthcheck");

			const responseTime = Date.now() - start;

			return {
				service: "clinicore_api",
				status: responseTime < 3000 ? "healthy" : "degraded",
				responseTime,
				details: {
					endpoint: "/patients/search",
				},
			};
		} catch (error) {
			return {
				service: "clinicore_api",
				status: "unhealthy",
				responseTime: Date.now() - start,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}

	/**
	 * Check memory usage
	 */
	private async checkMemoryUsage(): Promise<HealthCheckResult> {
		const start = Date.now();
		
		try {
			const memoryUsage = process.memoryUsage();
			const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
			const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
			const usagePercent = (heapUsedMB / heapTotalMB) * 100;

			let status: "healthy" | "degraded" | "unhealthy" = "healthy";
			if (usagePercent > 90) {
				status = "unhealthy";
			} else if (usagePercent > 75) {
				status = "degraded";
			}

			return {
				service: "memory",
				status,
				responseTime: Date.now() - start,
				details: {
					heapUsedMB: Math.round(heapUsedMB),
					heapTotalMB: Math.round(heapTotalMB),
					usagePercent: Math.round(usagePercent),
				},
			};
		} catch (error) {
			return {
				service: "memory",
				status: "unhealthy",
				responseTime: Date.now() - start,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}

	/**
	 * Calculate overall system health based on individual checks
	 */
	private calculateOverallHealth(checks: HealthCheckResult[]): "healthy" | "degraded" | "unhealthy" {
		const unhealthyCount = checks.filter(check => check.status === "unhealthy").length;
		const degradedCount = checks.filter(check => check.status === "degraded").length;

		if (unhealthyCount > 0) {
			return "unhealthy";
		}

		if (degradedCount > 0) {
			return "degraded";
		}

		return "healthy";
	}

	/**
	 * Get system metrics
	 */
	private async getMetrics() {
		const uptime = Date.now() - this.startTime;
		const memoryUsage = process.memoryUsage();

		// Get sync statistics for today
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		const [patientsToday, appointmentsToday, errorsToday] = await Promise.allSettled([
			this.db.query.patient.findMany({
				where: (patient, { gte }) => gte(patient.createdAt, today),
			}).then(results => results.length),
			
			this.db.query.appointment.findMany({
				where: (appointment, { gte }) => gte(appointment.createdAt, today),
			}).then(results => results.length),
			
			this.db.query.errorLogs.findMany({
				where: (errorLogs, { gte }) => gte(errorLogs.createdAt, today),
			}).then(results => results.length),
		]);

		// Calculate error rate (errors per hour over last 24 hours)
		const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
		const recentErrors = await this.db.query.errorLogs.findMany({
			where: (errorLogs, { gte }) => gte(errorLogs.createdAt, last24Hours),
		});

		const errorRate = recentErrors.length / 24; // errors per hour

		return {
			uptime,
			memoryUsage,
			errorRate: Math.round(errorRate * 100) / 100,
			syncStats: {
				patientsToday: patientsToday.status === "fulfilled" ? patientsToday.value : 0,
				appointmentsToday: appointmentsToday.status === "fulfilled" ? appointmentsToday.value : 0,
				errorsToday: errorsToday.status === "fulfilled" ? errorsToday.value : 0,
			},
		};
	}

	/**
	 * Log system metrics for monitoring
	 */
	async logMetrics(): Promise<void> {
		try {
			const health = await this.getSystemHealth();
			
			logger.info("System health check", {
				overall: health.overall,
				uptime: health.metrics.uptime,
				memoryUsageMB: Math.round(health.metrics.memoryUsage.heapUsed / 1024 / 1024),
				errorRate: health.metrics.errorRate,
				syncStats: health.metrics.syncStats,
				unhealthyServices: health.checks
					.filter(check => check.status === "unhealthy")
					.map(check => check.service),
			});
		} catch (error) {
			logger.error("Failed to log system metrics", {
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}

	/**
	 * Start periodic health monitoring
	 * @param intervalMinutes - Interval in minutes (default: 5)
	 */
	startPeriodicMonitoring(intervalMinutes: number = 5): void {
		const intervalMs = intervalMinutes * 60 * 1000;
		
		setInterval(() => {
			this.logMetrics().catch(error => {
				logger.error("Periodic monitoring failed", {
					error: error instanceof Error ? error.message : String(error),
				});
			});
		}, intervalMs);

		logger.info("Started periodic health monitoring", {
			intervalMinutes,
		});
	}
}

// Export singleton instance
export const monitoringService = new MonitoringService();
