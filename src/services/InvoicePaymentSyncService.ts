import type { TLocalPatient } from "@/lib/base/LocalPatient";
import type { GetInvoiceType, GetPaymentType } from "@libCC/CCTypes";
import { LocalPatient } from "@/lib/base/LocalPatient";
import { CCInvoiceRequest } from "@libCC/request/invoiceRequest";
import { CCPaymentRequest } from "@libCC/request/paymentRequest";
import { CCUserRequest } from "@libCC/request/userRequest";
import { CustomFieldsSyncService } from "./CustomFieldsSyncService";
import { logger } from "@/utils";
import { format } from "date-fns";

// Custom field names for invoice and payment data
const AP_INVOICE_CUSTOM_FIELDS = {
  LatestInvoicePDFURL: "Latest Invoice PDF URL",
  LastInvoiceGrossAmount: "Last Invoice Gross Amount",
  LastInvoiceDiscount: "Last Invoice Discount",
  LastInvoiceTotalAmount: "Last Invoice Total Amount",
  LatestPaymentStatus: "Latest Payment Status",
  LastInvoiceProducts: "Last Invoice Products",
  LastInvoiceDiagnosis: "Last Invoice Diagnosis",
  LastInvoiceTreatedBy: "Last Invoice Treated By",
};

const AP_PAYMENT_CUSTOM_FIELDS = {
  LatestPaymentStatus: "Latest Payment Status",
  LatestAmountPaid: "Latest Amount Paid",
  LatestPaymentDate: "Latest Payment Date",
  LatestPaymentPDFURL: "Latest Payment PDF URL",
};

const AP_LTV_CUSTOM_FIELD = "Lifetime Value";

export class InvoicePaymentSyncService extends LocalPatient {
  private ccInvoiceRequest: CCInvoiceRequest;
  private ccPaymentRequest: CCPaymentRequest;
  private ccUserRequest: CCUserRequest;
  private customFieldsService: CustomFieldsSyncService;

  constructor() {
    super();
    this.ccInvoiceRequest = new CCInvoiceRequest();
    this.ccPaymentRequest = new CCPaymentRequest();
    this.ccUserRequest = new CCUserRequest();
    this.customFieldsService = new CustomFieldsSyncService();
  }

  /**
   * Sync invoice and payment data to AutoPatient custom fields
   * Implements syncInvoicePayments from old helpers/ap.ts
   */
  async syncInvoicePayments(
    localPatient: TLocalPatient
  ): Promise<TLocalPatient> {
    try {
      if (!localPatient.apId || !localPatient.ccId) {
        logger.warn(
          "Unable to sync invoice to AP, Contact ID or Patient ID is missing",
          {
            patientId: localPatient.id,
            apContactId: localPatient.apId,
            ccPatientId: localPatient.ccId,
          }
        );
        return localPatient;
      }

      // Refresh CC patient data to get latest invoices and payments
      const ccPatient = await this.ccPatientRequest.get(localPatient.ccId);
      if (ccPatient?.id) {
        await this.db
          .update(this.dbSchema.patient)
          .set({
            ccData: ccPatient,
            ccUpdatedAt: new Date(ccPatient.updatedAt),
          })
          .where(this.db.eq(this.dbSchema.patient.id, localPatient.id));

        // Refresh local patient
        await this.refresh(localPatient.id);
      }

      // Sync invoice data
      await this.syncInvoice(this.patient!);

      // Sync payment data
      await this.syncPayment(this.patient!);

      return this.patient!;
    } catch (error) {
      logger.error("Failed to sync invoice payments", {
        patientId: localPatient.id,
        apContactId: localPatient.apId,
        ccPatientId: localPatient.ccId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Sync invoice data to AutoPatient custom fields
   * Implements syncInvoice from old helpers/ap.ts
   */
  private async syncInvoice(localPatient: TLocalPatient): Promise<void> {
    try {
      if (
        !localPatient.ccData?.invoices ||
        localPatient.ccData.invoices.length === 0
      ) {
        logger.info("No invoice to sync", {
          patientId: localPatient.id,
          apContactId: localPatient.apId,
          ccPatientId: localPatient.ccId,
        });
        return;
      }

      const allInvoices = await this.ccInvoiceRequest.getInvoices(
        localPatient.ccData.invoices
      );
      if (allInvoices && allInvoices.length > 0) {
        const latestInvoice = allInvoices[0]; // Assuming sorted by date
        await this.syncLastInvoice(localPatient, latestInvoice);
      }
    } catch (error) {
      logger.error("Failed to sync invoice", {
        patientId: localPatient.id,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Sync payment data to AutoPatient custom fields
   * Implements syncPayment from old helpers/ap.ts
   */
  private async syncPayment(localPatient: TLocalPatient): Promise<void> {
    try {
      if (
        !localPatient.ccData?.payments ||
        localPatient.ccData.payments.length === 0
      ) {
        logger.info("No payment to sync", {
          patientId: localPatient.id,
          apContactId: localPatient.apId,
          ccPatientId: localPatient.ccId,
        });
        return;
      }

      const allPayments = await this.ccPaymentRequest.getPayments(
        localPatient.ccData.payments
      );
      if (allPayments && allPayments.length > 0) {
        // Sync LTV (lifetime value)
        await this.syncLTV(localPatient, allPayments);

        // Sync latest payment
        const latestPayment = allPayments[0]; // Assuming sorted by date
        await this.syncLastPayment(localPatient, latestPayment);
      }
    } catch (error) {
      logger.error("Failed to sync payment", {
        patientId: localPatient.id,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Sync latest invoice data to AutoPatient custom fields
   * Implements syncLastInvoice from old helpers/ap.ts
   */
  private async syncLastInvoice(
    localPatient: TLocalPatient,
    invoice: GetInvoiceType
  ): Promise<void> {
    try {
      if (!invoice.positions || invoice.positions.length === 0) {
        return;
      }

      const totalAmount = invoice.positions.reduce(
        (sum, pos) => sum + pos.gross,
        0
      );
      const totalDiscount = invoice.positions.reduce(
        (sum, pos) => sum + (pos.discount || 0),
        0
      );

      const productNames = invoice.positions.map((pos) => pos.name);
      const diagnosisNames = invoice.diagnoses?.map((diag) => diag.text) || [];

      // Get practitioner name
      let treatedBy = "";
      if (invoice.practicioner) {
        try {
          const practitioner = await this.ccUserRequest.get(
            invoice.practicioner
          );
          treatedBy = practitioner?.shortName || "";
        } catch (error) {
          logger.warn("Failed to get practitioner info", {
            practitionerId: invoice.practicioner,
          });
        }
      }

      // Prepare custom fields data
      const customFieldsData = {
        [AP_INVOICE_CUSTOM_FIELDS.LatestInvoicePDFURL]: invoice.pdfUrl,
        [AP_INVOICE_CUSTOM_FIELDS.LastInvoiceGrossAmount]: totalAmount,
        [AP_INVOICE_CUSTOM_FIELDS.LastInvoiceDiscount]: totalDiscount,
        [AP_INVOICE_CUSTOM_FIELDS.LastInvoiceTotalAmount]:
          totalAmount - totalDiscount - (invoice.discount || 0),
        [AP_INVOICE_CUSTOM_FIELDS.LatestPaymentStatus]: invoice.status,
        [AP_INVOICE_CUSTOM_FIELDS.LastInvoiceProducts]:
          this.reduceCustomFieldValue(productNames),
        [AP_INVOICE_CUSTOM_FIELDS.LastInvoiceDiagnosis]:
          this.reduceCustomFieldValue(diagnosisNames),
        [AP_INVOICE_CUSTOM_FIELDS.LastInvoiceTreatedBy]: treatedBy,
      };

      // Sync to AP custom fields
      await this.customFieldsService.syncCCToAPCustomFields(
        localPatient,
        customFieldsData
      );

      logger.info("Latest invoice updated", {
        patientId: localPatient.id,
        apContactId: localPatient.apId,
        ccPatientId: localPatient.ccId,
        invoiceId: invoice.id,
      });
    } catch (error) {
      logger.error("Failed to sync last invoice", {
        patientId: localPatient.id,
        invoiceId: invoice.id,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Sync latest payment data to AutoPatient custom fields
   * Implements syncLastPayment from old helpers/ap.ts
   */
  private async syncLastPayment(
    localPatient: TLocalPatient,
    payment: GetPaymentType
  ): Promise<void> {
    try {
      if (!payment.invoicePayments?.length && !payment.gross) {
        return;
      }

      const latestPayment = payment.invoicePayments?.length
        ? payment.invoicePayments[payment.invoicePayments.length - 1].gross
        : payment.gross;

      // Format payment date
      const paymentDate = format(
        new Date(payment.createdAt),
        "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"
      );

      // Prepare custom fields data
      const customFieldsData = {
        [AP_PAYMENT_CUSTOM_FIELDS.LatestPaymentStatus]: "Success",
        [AP_PAYMENT_CUSTOM_FIELDS.LatestAmountPaid]: Math.abs(latestPayment),
        [AP_PAYMENT_CUSTOM_FIELDS.LatestPaymentDate]: paymentDate,
        [AP_PAYMENT_CUSTOM_FIELDS.LatestPaymentPDFURL]: payment.pdfUrl,
      };

      // Sync to AP custom fields
      await this.customFieldsService.syncCCToAPCustomFields(
        localPatient,
        customFieldsData
      );

      logger.info("Latest payment updated", {
        patientId: localPatient.id,
        apContactId: localPatient.apId,
        ccPatientId: localPatient.ccId,
        paymentId: payment.id,
      });
    } catch (error) {
      logger.error("Failed to sync last payment", {
        patientId: localPatient.id,
        paymentId: payment.id,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Calculate and sync lifetime value (LTV)
   * Implements syncLtv from old helpers/ap.ts
   */
  private async syncLTV(
    localPatient: TLocalPatient,
    payments: GetPaymentType[]
  ): Promise<void> {
    try {
      let ltv = 0;

      payments.forEach((payment) => {
        if (
          !payment.canceled &&
          !payment.reversedBy &&
          !payment.reverses &&
          payment.gross > 0 &&
          payment.patient === localPatient.ccId
        ) {
          ltv += payment.gross;
        }
      });

      // Sync LTV to AP custom fields
      const customFieldsData = {
        [AP_LTV_CUSTOM_FIELD]: ltv,
      };

      await this.customFieldsService.syncCCToAPCustomFields(
        localPatient,
        customFieldsData
      );

      logger.info("LTV calculated and synced", {
        patientId: localPatient.id,
        ccPatientId: localPatient.ccId,
        ltv: ltv,
      });
    } catch (error) {
      logger.error("Failed to sync LTV", {
        patientId: localPatient.id,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Reduce custom field value to fit field limits
   * Implements reduceCustomFieldValue from old utils
   */
  private reduceCustomFieldValue(values: string[]): string {
    const joined = values.join(", ");
    // Limit to 255 characters for custom field values
    return joined.length > 255 ? joined.substring(0, 252) + "..." : joined;
  }
}
