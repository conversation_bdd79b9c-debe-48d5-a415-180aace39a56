import type { TLocalPatient } from "@/lib/base/LocalPatient";
import type {
  PostAPContactType,
  APGetCustomFieldType,
  GetAPContactType,
} from "@libAP/APTypes";
import type {
  PostCCPatientCustomfield,
  GetCCCustomField,
} from "@libCC/CCTypes";
import { LocalPatient } from "@/lib/base/LocalPatient";
import { APCustomFieldRequest } from "@libAP/requests/customFieldRequest";
import { APContactRequest } from "@libAP/requests/contactRequest";
import { CCCustomFieldRequest } from "@libCC/request/customFieldRequest";
import { CCPatientRequest } from "@libCC/request/patientRequest";
import { logger } from "@/utils";
import { cleanData } from "@/utils/cleanData";
import { eq } from "drizzle-orm";

export class CustomFieldsSyncService extends LocalPatient {
  private apCustomFieldRequest: APCustomFieldRequest;
  private apContactRequest: APContactRequest;
  private ccCustomFieldRequest: CCCustomFieldRequest;
  private ccPatientRequest: CCPatientRequest;

  constructor() {
    super();
    this.apCustomFieldRequest = new APCustomFieldRequest();
    this.apContactRequest = new APContactRequest();
    this.ccCustomFieldRequest = new CCCustomFieldRequest();
    this.ccPatientRequest = new CCPatientRequest();
  }

  /**
   * Sync CliniCore custom fields to AutoPatient
   * Implements syncCCtoAPCustomfields logic from old helpers/ap.ts
   */
  async syncCCToAPCustomFields(
    localPatient: TLocalPatient,
    extraFields: Record<string, any> = {}
  ): Promise<void> {
    try {
      if (!localPatient.apId || !localPatient.ccId) {
        throw new Error(
          "AP Contact ID or CC Patient ID is missing, unable to sync custom fields"
        );
      }

      // Get CC custom fields label-value pairs
      let ccNameValue = await this.getCCCustomFieldsLabelValue(localPatient);

      if (extraFields && Object.keys(extraFields).length > 0) {
        ccNameValue = { ...ccNameValue, ...extraFields };
      }

      // Add computed fields
      ccNameValue["Total Appointments"] =
        localPatient.ccData?.appointments?.length || 0;
      ccNameValue["Patient ID"] = localPatient.ccData?.id;

      // Get service-specific appointment counts and spends
      const services = await this.getIndividualServiceAppointmentCount(
        localPatient
      );
      const spends = await this.getIndividualServiceSpends(localPatient);

      if (Object.keys(services).length > 0) {
        ccNameValue = { ...ccNameValue, ...services };
      }
      if (Object.keys(spends).length > 0) {
        ccNameValue = { ...ccNameValue, ...spends };
      }

      // Get AP custom field name-to-ID mapping
      const apNameId = await this.getAPCustomFieldsNameId();
      const payload: PostAPContactType = {
        customFields: [],
      };

      // Map CC fields to AP custom fields
      if (Object.keys(ccNameValue).length > 0) {
        for (const [key, value] of Object.entries(ccNameValue)) {
          if (key in apNameId && payload.customFields) {
            payload.customFields.push({
              id: apNameId[key],
              value: value,
            });
          } else {
            // Create new custom field if it doesn't exist
            logger.info(`Creating new AP custom field: ${key}`);
            const cfRes = await this.apCustomFieldRequest.create({
              name: key,
              dataType: "TEXT",
            });
            if (cfRes?.id) {
              payload.customFields?.push({
                id: cfRes.id,
                value: value,
              });
            }
          }
        }
      }

      // Update contact with custom fields
      if (payload.customFields && payload.customFields.length > 0) {
        const updateRes = await this.apContactRequest.update(
          localPatient.apId,
          payload
        );
        if (updateRes) {
          // Update local patient with new AP data
          await this.db
            .update(this.dbSchema.patient)
            .set({
              apData: updateRes,
              apUpdatedAt: new Date(updateRes.dateUpdated || new Date()),
            })
            .where(eq(this.dbSchema.patient.id, localPatient.id));

          logger.info("CC to AP custom fields synced successfully", {
            patientId: localPatient.id,
            apContactId: localPatient.apId,
            fieldsCount: payload.customFields.length,
          });
        }
      } else {
        logger.info("No custom fields found to sync", {
          patientId: localPatient.id,
          apContactId: localPatient.apId,
        });
      }
    } catch (error) {
      logger.error("Failed to sync CC to AP custom fields", {
        patientId: localPatient.id,
        apContactId: localPatient.apId,
        ccPatientId: localPatient.ccId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Sync AutoPatient custom fields to CliniCore
   * Implements syncApToCcCustomfields logic from old helpers/cc.ts
   */
  async syncAPToCCCustomFields(
    localPatient: TLocalPatient,
    returnValue = false
  ): Promise<PostCCPatientCustomfield[] | void> {
    try {
      const apCustomFields = await this.apCustomFieldRequest.all();
      const apCFNameValue: Record<string, any> = {};

      // Extract AP custom field values
      if (
        localPatient.apData?.customFields &&
        localPatient.apData.customFields.length > 0
      ) {
        localPatient.apData.customFields.forEach((cf) => {
          const match = apCustomFields.find((apcf) => apcf.id === cf.id);
          if (match) {
            apCFNameValue[match.name] = cf.value;
          }
        });
      }

      // Add contact info fields
      apCFNameValue["email"] = localPatient.email;
      apCFNameValue["phoneMobile"] = localPatient.phone;
      apCFNameValue["phone-mobile"] = localPatient.phone;
      apCFNameValue["phone"] = localPatient.phone;

      // Get CC custom fields for mapping
      const ccCustomFields =
        await this.ccCustomFieldRequest.getAllCustomFields();
      const matchedProperties: PostCCPatientCustomfield[] = [];

      // Map AP fields to CC custom fields
      Object.entries(apCFNameValue).forEach(([fieldName, fieldValue]) => {
        const match = ccCustomFields.find(
          (ccf) => ccf.name === fieldName || ccf.label === fieldName
        );
        if (match) {
          const value: PostCCPatientCustomfield = {
            field: match,
            values: [{ value: fieldValue }],
            patient: null,
          };

          // Handle allowed values (dropdown fields)
          if (match.allowedValues && match.allowedValues.length > 0) {
            const allowedValue = match.allowedValues.find(
              (v) => v.value === fieldValue
            );
            if (allowedValue) {
              value.values = [{ id: allowedValue.id }];
            }
          }

          matchedProperties.push(value);
        }
      });

      const payload = cleanData(matchedProperties);

      if (returnValue) {
        return payload;
      }

      if (!localPatient.ccId) {
        logger.warn(
          "Patient doesn't have CC ID, preventing custom fields sync",
          {
            patientId: localPatient.id,
          }
        );
        return;
      }

      // Update patient custom fields in CC
      const ccRes = await this.ccPatientRequest.update(localPatient.ccId, {
        customFields: payload,
      });

      if (ccRes) {
        // Update local patient with new CC data
        await this.db
          .update(this.dbSchema.patient)
          .set({
            ccData: ccRes,
            ccUpdatedAt: new Date(ccRes.updatedAt),
          })
          .where(eq(this.dbSchema.patient.id, localPatient.id));

        logger.info("AP to CC custom fields synced successfully", {
          patientId: localPatient.id,
          ccPatientId: localPatient.ccId,
          fieldsCount: payload.length,
        });
      } else {
        logger.warn("Unable to update patient custom fields in CC", {
          patientId: localPatient.id,
          ccPatientId: localPatient.ccId,
        });
      }
    } catch (error) {
      logger.error("Failed to sync AP to CC custom fields", {
        patientId: localPatient.id,
        apContactId: localPatient.apId,
        ccPatientId: localPatient.ccId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get CC custom fields as label-value pairs
   * Implements getCCCustomfieldsLabelValue from old helpers/cc.ts
   */
  private async getCCCustomFieldsLabelValue(
    localPatient: TLocalPatient
  ): Promise<Record<string, any>> {
    if (!localPatient.ccId) {
      throw new Error(
        `Patient ID not found while getting custom fields, Patient ID: ${localPatient.id}`
      );
    }

    if (
      !localPatient.ccData?.customFields ||
      localPatient.ccData.customFields.length < 1
    ) {
      logger.info("Patient doesn't have custom fields", {
        patientId: localPatient.id,
        ccPatientId: localPatient.ccId,
      });
      return {};
    }

    const patientCustomFields = await this.ccPatientRequest.getCustomFields(
      localPatient.ccData.customFields
    );
    const labelValue: Record<string, any> = {};

    if (patientCustomFields.length > 0) {
      patientCustomFields.forEach((cf) => {
        const label = cf.field.label;
        const values =
          cf.values.length > 0
            ? cf.values.map((v) => v.value).join(", ")
            : null;
        labelValue[label] = values;
      });
    }

    return labelValue;
  }

  /**
   * Get AP custom fields name-to-ID mapping
   * Implements getApCustomfieldsNameId from old helpers/ap.ts
   */
  private async getAPCustomFieldsNameId(): Promise<Record<string, string>> {
    const customFields = await this.apCustomFieldRequest.all();
    const nameId: Record<string, string> = {};

    if (customFields.length > 0) {
      customFields.forEach((cf) => {
        nameId[cf.name] = cf.id;
      });
    }

    return nameId;
  }

  /**
   * Get individual service appointment counts
   * Implements individualServiceAppointmentCount from old helpers/ap.ts
   */
  private async getIndividualServiceAppointmentCount(
    localPatient: TLocalPatient
  ): Promise<Record<string, number>> {
    // Implementation would require appointment and service data
    // This is a placeholder for the complex logic from the old helpers
    return {};
  }

  /**
   * Get individual service spending amounts
   * Implements individualServiceSpends from old helpers/ap.ts
   */
  private async getIndividualServiceSpends(
    localPatient: TLocalPatient
  ): Promise<Record<string, number>> {
    // Implementation would require invoice and service data
    // This is a placeholder for the complex logic from the old helpers
    return {};
  }
}
