import type { TLocalAppointment } from "@/lib/base/LocalAppointment";
import type { TLocalPatient } from "@/lib/base/LocalPatient";
import type { GetAPAppointmentType, PostAPAppointmentType } from "@libAP/APTypes";
import type { GetCCAppointmentType, PostCCAppointmentType } from "@libCC/CCTypes";
import { LocalAppointment } from "@/lib/base/LocalAppointment";
import { APAppointmentRequest } from "@libAP/requests/appointmentRequest";
import { APNoteRequest } from "@libAP/requests/noteRequest";
import { CCAppointmentRequest } from "@libCC/request/appointmentRequest";
import { PatientSyncService } from "./PatientSyncService";
import { CustomFieldsSyncService } from "./CustomFieldsSyncService";
import { logger } from "@/utils";
import { cleanData } from "@/utils/cleanData";
import { eq } from "drizzle-orm";
import { format, parseISO } from "date-fns";

export class AppointmentSyncService extends LocalAppointment {
	private apAppointmentRequest: APAppointmentRequest;
	private apNoteRequest: APNoteRequest;
	private ccAppointmentRequest: CCAppointmentRequest;
	private patientSyncService: PatientSyncService;
	private customFieldsService: CustomFieldsSyncService;

	constructor() {
		super();
		this.apAppointmentRequest = new APAppointmentRequest();
		this.apNoteRequest = new APNoteRequest();
		this.ccAppointmentRequest = new CCAppointmentRequest();
		this.patientSyncService = new PatientSyncService();
		this.customFieldsService = new CustomFieldsSyncService();
	}

	/**
	 * Sync CliniCore appointment to AutoPatient
	 * Implements syncCCtoAPAppointment logic from old helpers/ap.ts
	 */
	async syncCCAppointmentToAP(localAppointment: TLocalAppointment): Promise<TLocalAppointment> {
		try {
			if (localAppointment.apId) {
				return await this.updateAppointmentInAP(localAppointment);
			}
			return await this.createAppointmentInAP(localAppointment);
		} catch (error) {
			logger.error("Failed to sync CC appointment to AP", {
				appointmentId: localAppointment.id,
				ccAppointmentId: localAppointment.ccId,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Create appointment in AutoPatient
	 * Implements createAppointmentToAP from old helpers/ap.ts
	 */
	private async createAppointmentInAP(localAppointment: TLocalAppointment): Promise<TLocalAppointment> {
		try {
			if (localAppointment.apId) {
				logger.info("Appointment already exists in AP", {
					appointmentId: localAppointment.id,
					apId: localAppointment.apId,
				});
				return localAppointment;
			}

			// Ensure patient exists in AP
			const localPatient = await this.patientSyncService.getPatientById(localAppointment.patientId!);
			if (!localPatient) {
				throw new Error("Patient not found for appointment");
			}

			if (!localPatient.apId) {
				await this.patientSyncService.syncCCPatientToAP(localPatient);
				await this.patientSyncService.refresh(localPatient.id);
			}

			const startTime = this.getStartTime();
			const endTime = this.getEndTime();

			if (!startTime || !endTime) {
				throw new Error("Invalid appointment times");
			}

			const payload: PostAPAppointmentType = {
				contactId: localPatient.apId!,
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				appointmentStatus: this.isCancelled() ? "cancelled" : "confirmed",
			};

			// Set appointment title with patient type prefix
			if (this.ccAppointment?.title) {
				const titlePrefix = this.ccAppointment.firstOfPatient ? "Neukunde: " : "Bestandskunde: ";
				payload.title = titlePrefix + this.removeHtmlTags(this.ccAppointment.title);
			}

			const apAppointment = await this.apAppointmentRequest.create(payload);
			if (!apAppointment?.id) {
				throw new Error("Failed to create appointment in AutoPatient");
			}

			// Update local appointment with AP data
			const updated = await this.db
				.update(this.dbSchema.appointment)
				.set({
					apId: apAppointment.id,
					apData: apAppointment,
					apUpdatedAt: new Date(apAppointment.dateUpdated || new Date()),
				})
				.where(eq(this.dbSchema.appointment.id, localAppointment.id))
				.returning();

			this.appointment = updated[0];

			// Add tags to patient based on appointment
			await this.addAppointmentTagsToPatient(localPatient);

			// Create note in AutoPatient
			await this.createAppointmentNote(localPatient.apId!, "created");

			// Sync appointment custom fields
			await this.syncAppointmentCustomFieldsToAP();

			logger.info("Appointment created in AP successfully", {
				appointmentId: localAppointment.id,
				apId: apAppointment.id,
				ccId: localAppointment.ccId,
			});

			return this.appointment;
		} catch (error) {
			logger.error("Failed to create appointment in AP", {
				appointmentId: localAppointment.id,
				ccAppointmentId: localAppointment.ccId,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Update appointment in AutoPatient
	 * Implements updateAppointmentToAP from old helpers/ap.ts
	 */
	private async updateAppointmentInAP(localAppointment: TLocalAppointment): Promise<TLocalAppointment> {
		try {
			if (!localAppointment.apId) {
				logger.info("Appointment ID missing, creating new appointment", {
					appointmentId: localAppointment.id,
				});
				return await this.createAppointmentInAP(localAppointment);
			}

			const startTime = this.getStartTime();
			const endTime = this.getEndTime();

			if (!startTime || !endTime) {
				throw new Error("Invalid appointment times");
			}

			const payload: any = {
				startTime: startTime.toISOString(),
				endTime: endTime.toISOString(),
				appointmentStatus: this.isCancelled() ? "cancelled" : "confirmed",
			};

			// Set appointment title with patient type prefix
			if (this.ccAppointment?.title) {
				const titlePrefix = this.ccAppointment.firstOfPatient ? "Neukunde: " : "Bestandskunde: ";
				payload.title = titlePrefix + this.removeHtmlTags(this.ccAppointment.title);
			}

			const apAppointment = await this.apAppointmentRequest.update(localAppointment.apId, payload);
			if (!apAppointment?.id) {
				throw new Error("Failed to update appointment in AutoPatient");
			}

			// Update local appointment with AP data
			const updated = await this.db
				.update(this.dbSchema.appointment)
				.set({
					apData: apAppointment,
					apUpdatedAt: new Date(apAppointment.dateUpdated || new Date()),
				})
				.where(eq(this.dbSchema.appointment.id, localAppointment.id))
				.returning();

			this.appointment = updated[0];

			// Update note if exists
			if (localAppointment.apNoteID) {
				const localPatient = await this.patientSyncService.getPatientById(localAppointment.patientId!);
				if (localPatient?.apId) {
					await this.updateAppointmentNote(localPatient.apId, localAppointment.apNoteID);
				}
			}

			logger.info("Appointment updated in AP successfully", {
				appointmentId: localAppointment.id,
				apId: localAppointment.apId,
			});

			return this.appointment;
		} catch (error) {
			logger.error("Failed to update appointment in AP", {
				appointmentId: localAppointment.id,
				apId: localAppointment.apId,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Delete appointment from AutoPatient
	 * Implements deleteAppointmentFromAP from old helpers/ap.ts
	 */
	async deleteAppointmentFromAP(localAppointment: TLocalAppointment): Promise<void> {
		try {
			if (!localAppointment.apId) {
				logger.info("Appointment doesn't exist in AP", {
					appointmentId: localAppointment.id,
					ccId: localAppointment.ccId,
				});
				return;
			}

			await this.apAppointmentRequest.delete(localAppointment.apId);

			logger.info("Appointment deleted from AP successfully", {
				appointmentId: localAppointment.id,
				apId: localAppointment.apId,
			});
		} catch (error) {
			logger.error("Failed to delete appointment from AP", {
				appointmentId: localAppointment.id,
				apId: localAppointment.apId,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Sync AutoPatient appointment to CliniCore
	 * Implements createAppointmentToCC from old helpers/cc.ts
	 */
	async syncAPAppointmentToCC(localAppointment: TLocalAppointment): Promise<TLocalAppointment> {
		try {
			if (localAppointment.ccId) {
				return await this.updateAppointmentInCC(localAppointment);
			}
			return await this.createAppointmentInCC(localAppointment);
		} catch (error) {
			logger.error("Failed to sync AP appointment to CC", {
				appointmentId: localAppointment.id,
				apAppointmentId: localAppointment.apId,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Create appointment in CliniCore
	 */
	private async createAppointmentInCC(localAppointment: TLocalAppointment): Promise<TLocalAppointment> {
		try {
			if (localAppointment.ccId) {
				logger.info("Appointment already exists in CC", {
					appointmentId: localAppointment.id,
					ccId: localAppointment.ccId,
				});
				return localAppointment;
			}

			// Ensure patient exists in CC
			const localPatient = await this.patientSyncService.getPatientById(localAppointment.patientId!);
			if (!localPatient) {
				throw new Error("Patient not found for appointment");
			}

			if (!localPatient.ccId) {
				await this.patientSyncService.syncAPContactToCC(localPatient);
				await this.patientSyncService.refresh(localPatient.id);
			}

			const startTime = this.getStartTime();
			const endTime = this.getEndTime();

			if (!startTime || !endTime) {
				throw new Error("Invalid appointment times");
			}

			const payload: PostCCAppointmentType = {
				patients: [localPatient.ccId!],
				startsAt: startTime.toISOString(),
				endsAt: endTime.toISOString(),
			};

			const ccAppointment = await this.ccAppointmentRequest.create(payload);
			if (!ccAppointment?.id) {
				throw new Error("Failed to create appointment in CliniCore");
			}

			// Update local appointment with CC data
			const updated = await this.db
				.update(this.dbSchema.appointment)
				.set({
					ccId: ccAppointment.id,
					ccData: ccAppointment,
					ccUpdatedAt: new Date(ccAppointment.updatedAt || new Date()),
				})
				.where(eq(this.dbSchema.appointment.id, localAppointment.id))
				.returning();

			this.appointment = updated[0];

			// Sync appointment custom fields
			await this.syncAppointmentCustomFieldsToCC();

			logger.info("Appointment created in CC successfully", {
				appointmentId: localAppointment.id,
				ccId: ccAppointment.id,
				apId: localAppointment.apId,
			});

			return this.appointment;
		} catch (error) {
			logger.error("Failed to create appointment in CC", {
				appointmentId: localAppointment.id,
				apAppointmentId: localAppointment.apId,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Update appointment in CliniCore
	 */
	private async updateAppointmentInCC(localAppointment: TLocalAppointment): Promise<TLocalAppointment> {
		try {
			if (!localAppointment.ccId) {
				logger.info("Appointment ID missing, creating new appointment", {
					appointmentId: localAppointment.id,
				});
				return await this.createAppointmentInCC(localAppointment);
			}

			// Handle cancellation
			if (this.apAppointment?.appointmentStatus === "cancelled") {
				return await this.cancelAppointmentInCC(localAppointment);
			}

			const startTime = this.getStartTime();
			const endTime = this.getEndTime();

			if (!startTime || !endTime) {
				throw new Error("Invalid appointment times");
			}

			const payload: any = {
				startsAt: startTime.toISOString(),
				endsAt: endTime.toISOString(),
				canceledWhy: null,
			};

			const ccAppointment = await this.ccAppointmentRequest.update(localAppointment.ccId, payload);
			if (!ccAppointment?.id) {
				throw new Error("Failed to update appointment in CliniCore");
			}

			// Update local appointment with CC data
			const updated = await this.db
				.update(this.dbSchema.appointment)
				.set({
					ccData: ccAppointment,
					ccUpdatedAt: new Date(ccAppointment.updatedAt || new Date()),
				})
				.where(eq(this.dbSchema.appointment.id, localAppointment.id))
				.returning();

			this.appointment = updated[0];

			// Sync appointment custom fields
			await this.syncAppointmentCustomFieldsToCC();

			logger.info("Appointment updated in CC successfully", {
				appointmentId: localAppointment.id,
				ccId: localAppointment.ccId,
			});

			return this.appointment;
		} catch (error) {
			logger.error("Failed to update appointment in CC", {
				appointmentId: localAppointment.id,
				ccId: localAppointment.ccId,
				error: error.message,
			});
			throw error;
		}
	}

	/**
	 * Cancel appointment in CliniCore
	 */
	private async cancelAppointmentInCC(localAppointment: TLocalAppointment): Promise<TLocalAppointment> {
		const payload = {
			canceledWhy: "cancelled at autoPatient",
		};

		const ccAppointment = await this.ccAppointmentRequest.update(localAppointment.ccId!, payload);
		if (ccAppointment?.id) {
			const updated = await this.db
				.update(this.dbSchema.appointment)
				.set({
					ccData: ccAppointment,
					ccUpdatedAt: new Date(ccAppointment.updatedAt || new Date()),
				})
				.where(eq(this.dbSchema.appointment.id, localAppointment.id))
				.returning();

			this.appointment = updated[0];
			
			logger.info("Appointment cancelled in CC", {
				appointmentId: localAppointment.id,
				ccId: localAppointment.ccId,
			});
		}

		return this.appointment!;
	}

	/**
	 * Add appointment-related tags to patient in AutoPatient
	 */
	private async addAppointmentTagsToPatient(localPatient: TLocalPatient): Promise<void> {
		// Implementation would require service and tag management
		// This is a placeholder for the complex logic from the old helpers
	}

	/**
	 * Create appointment note in AutoPatient
	 */
	private async createAppointmentNote(contactId: string, action: string): Promise<void> {
		// Implementation would require note creation logic
		// This is a placeholder for the complex logic from the old helpers
	}

	/**
	 * Update appointment note in AutoPatient
	 */
	private async updateAppointmentNote(contactId: string, noteId: string): Promise<void> {
		// Implementation would require note update logic
		// This is a placeholder for the complex logic from the old helpers
	}

	/**
	 * Sync appointment custom fields to AutoPatient
	 */
	private async syncAppointmentCustomFieldsToAP(): Promise<void> {
		// Implementation would require complex custom field mapping
		// This is a placeholder for the complex logic from the old helpers
	}

	/**
	 * Sync appointment custom fields to CliniCore
	 */
	private async syncAppointmentCustomFieldsToCC(): Promise<void> {
		// Implementation would require complex custom field mapping
		// This is a placeholder for the complex logic from the old helpers
	}

	/**
	 * Remove HTML tags from text
	 */
	private removeHtmlTags(text: string): string {
		return text.replace(/<[^>]*>/g, "");
	}
}
