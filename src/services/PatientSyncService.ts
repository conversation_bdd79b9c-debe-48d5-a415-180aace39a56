import type { GetCCPatientType, GetAPContactType, PostAPContactType, PostCCPatientType } from "@/lib";
import type { TLocalPatient } from "@/lib/base/LocalPatient";
import { LocalPatient } from "@/lib/base/LocalPatient";
import { APContactRequest } from "@libAP/requests/contactRequest";
import { CCPatientRequest } from "@libCC/request/patientRequest";
import { CustomFieldsSyncService } from "./CustomFieldsSyncService";
import { InvoicePaymentSyncService } from "./InvoicePaymentSyncService";
import { logger } from "@/utils";
import { eq } from "drizzle-orm";

export class PatientSyncService extends LocalPatient {
	private apContactRequest: APContactRequest;
	private ccPatientRequest: CCPatientRequest;
	private customFieldsService: CustomFieldsSyncService;
	private invoicePaymentService: InvoicePaymentSyncService;

	constructor() {
		super();
		this.apContactRequest = new APContactRequest();
		this.ccPatientRequest = new CCPatientRequest();
		this.customFieldsService = new CustomFieldsSyncService();
		this.invoicePaymentService = new InvoicePaymentSyncService();
	}

	/**
	 * Sync CliniCore patient to AutoPatient
	 * Implements the updateOrCreateContact logic from old helpers/ap.ts
	 */
	async syncCCPatientToAP(localPatient: TLocalPatient): Promise<TLocalPatient> {
		try {
			if (!localPatient.ccData) {
				throw new Error("Required CC data is missing while creating contact");
			}

			if (!localPatient.email && !localPatient.phone) {
				throw new Error("Invalid contact data, email and phone is missing.");
			}

			const payload: PostAPContactType = {
				email: localPatient.email,
				phone: localPatient.phone,
				firstName: localPatient.ccData.firstName,
				lastName: localPatient.ccData.lastName,
				tags: ["cc_api"],
				dateOfBirth: localPatient.ccData.dob,
			};

			let apContact: GetAPContactType;

			if (!localPatient.apId) {
				// Create new contact in AutoPatient
				payload.source = "cc";
				payload.gender = localPatient.ccData.gender;
				apContact = await this.apContactRequest.upsert(payload);
				
				logger.info("Created new contact in AutoPatient", {
					localPatientId: localPatient.id,
					apContactId: apContact.id,
				});
			} else {
				// Update existing contact in AutoPatient
				payload.tags = [...(payload.tags ?? []), ...(localPatient.apData?.tags ?? [])];
				apContact = await this.apContactRequest.update(localPatient.apId, payload);
				
				logger.info("Updated existing contact in AutoPatient", {
					localPatientId: localPatient.id,
					apContactId: apContact.id,
				});
			}

			if (!apContact) {
				throw new Error("Unable to create/update contact to AP");
			}

			// Update local patient with AP data
			const updatedPatient = await this.db
				.update(this.dbSchema.patient)
				.set({
					apId: apContact.id,
					apData: apContact,
					apUpdatedAt: new Date(apContact.dateUpdated || new Date()),
				})
				.where(eq(this.dbSchema.patient.id, localPatient.id))
				.returning();

			const refreshedPatient = updatedPatient[0];

			// Sync custom fields
			await this.customFieldsService.syncCCToAPCustomFields(refreshedPatient);

			// Sync invoice and payment data
			await this.invoicePaymentService.syncInvoicePayments(refreshedPatient);

			logger.info("Patient synced to AP successfully", {
				localPatientId: refreshedPatient.id,
				apContactId: refreshedPatient.apId,
			});

			return refreshedPatient;
		} catch (error) {
			logger.error("Failed to sync CC patient to AP", {
				localPatientId: localPatient.id,
				ccPatientId: localPatient.ccId,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Sync AutoPatient contact to CliniCore
	 * Implements the searchPatient and updatePatientToCC logic from old helpers/cc.ts
	 */
	async syncAPContactToCC(localPatient: TLocalPatient): Promise<TLocalPatient> {
		try {
			if (!localPatient.apData) {
				throw new Error("Required AP data is missing while creating patient");
			}

			if (!localPatient.email && !localPatient.phone) {
				throw new Error("Invalid data: Email and phone is missing");
			}

			let ccPatient: GetCCPatientType | null = null;

			// Search for existing patient in CliniCore
			if (localPatient.email) {
				ccPatient = await this.ccPatientRequest.search(localPatient.email);
			}
			if (!ccPatient && localPatient.phone) {
				ccPatient = await this.ccPatientRequest.search(localPatient.phone);
			}

			const payload: PostCCPatientType = {
				firstName: localPatient.apData.firstName,
				lastName: localPatient.apData.lastName,
				email: localPatient.email,
				phoneMobile: localPatient.phone,
			};

			// Add custom fields from AP to CC
			if (localPatient.apId) {
				payload.customFields = await this.customFieldsService.syncAPToCCCustomFields(localPatient, true);
			}

			if (!ccPatient) {
				// Create new patient in CliniCore
				logger.info("Creating new patient in CliniCore", {
					localPatientId: localPatient.id,
					email: localPatient.email,
				});
				ccPatient = await this.ccPatientRequest.create(payload);
			} else if (localPatient.ccId) {
				// Update existing patient in CliniCore
				logger.info("Updating existing patient in CliniCore", {
					localPatientId: localPatient.id,
					ccPatientId: localPatient.ccId,
				});
				ccPatient = await this.ccPatientRequest.update(localPatient.ccId, payload);
			}

			if (!ccPatient) {
				throw new Error("Unable to create/update patient in CC");
			}

			// Update local patient with CC data
			const updatedPatient = await this.db
				.update(this.dbSchema.patient)
				.set({
					ccId: ccPatient.id,
					ccData: ccPatient,
					ccUpdatedAt: new Date(ccPatient.updatedAt),
				})
				.where(eq(this.dbSchema.patient.id, localPatient.id))
				.returning();

			logger.info("Patient synced to CC successfully", {
				localPatientId: updatedPatient[0].id,
				ccPatientId: updatedPatient[0].ccId,
			});

			return updatedPatient[0];
		} catch (error) {
			logger.error("Failed to sync AP contact to CC", {
				localPatientId: localPatient.id,
				apContactId: localPatient.apId,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Find or create patient by email/phone
	 * Used for appointment sync when patient might not exist locally
	 */
	async findOrCreatePatientByCCId(ccPatientId: number): Promise<TLocalPatient> {
		try {
			// First check if patient exists locally
			let localPatient = await this.getPatientByCCId(ccPatientId);
			
			if (localPatient) {
				return localPatient;
			}

			// Fetch patient from CliniCore
			const ccPatient = await this.ccPatientRequest.get(ccPatientId);
			if (!ccPatient) {
				throw new Error(`Patient ${ccPatientId} does not exist in CliniCore`);
			}

			if (!ccPatient.email && !ccPatient.phoneMobile) {
				throw new Error("Invalid data: Email and phone is empty for patient");
			}

			// Create local patient and sync to AP
			await this.upsertLocalFromCC(ccPatient);
			const syncedPatient = await this.syncCCPatientToAP(this.patient!);

			logger.info("Created and synced new patient from CC", {
				ccPatientId: ccPatientId,
				localPatientId: syncedPatient.id,
				apContactId: syncedPatient.apId,
			});

			return syncedPatient;
		} catch (error) {
			logger.error("Failed to find or create patient by CC ID", {
				ccPatientId: ccPatientId,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Find or create patient by AP contact ID
	 * Used for reverse sync from AutoPatient
	 */
	async findOrCreatePatientByAPId(apContactId: string): Promise<TLocalPatient> {
		try {
			// First check if patient exists locally
			let localPatient = await this.getPatientByAPId(apContactId);
			
			if (localPatient) {
				return localPatient;
			}

			// Fetch contact from AutoPatient
			const apContact = await this.apContactRequest.get(apContactId);
			if (!apContact) {
				throw new Error(`Contact ${apContactId} does not exist in AutoPatient`);
			}

			// Create local patient and sync to CC if needed
			await this.upsertLocalFromAP(apContact);
			
			// Only sync to CC if this contact wasn't originally from CC
			if (apContact.source !== "cc") {
				const syncedPatient = await this.syncAPContactToCC(this.patient!);
				
				logger.info("Created and synced new patient from AP", {
					apContactId: apContactId,
					localPatientId: syncedPatient.id,
					ccPatientId: syncedPatient.ccId,
				});

				return syncedPatient;
			}

			logger.info("Created local patient from AP (CC origin)", {
				apContactId: apContactId,
				localPatientId: this.patient!.id,
			});

			return this.patient!;
		} catch (error) {
			logger.error("Failed to find or create patient by AP ID", {
				apContactId: apContactId,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}
}
