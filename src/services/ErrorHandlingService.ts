/**
 * Comprehensive error handling service
 * Provides centralized error handling, logging, and monitoring
 */

import { logger } from "@/utils";
import { dbSchema, getDb } from "@database";

export interface ErrorContext {
	service?: string;
	method?: string;
	patientId?: string;
	appointmentId?: string;
	webhookType?: string;
	requestId?: string;
	userId?: string;
	[key: string]: any;
}

export interface ErrorDetails {
	message: string;
	stack?: string;
	code?: string;
	statusCode?: number;
	context?: ErrorContext;
	originalError?: Error;
}

export class ErrorHandlingService {
	private db = getDb();

	/**
	 * Handle and log errors with context
	 * @param error - Error object or message
	 * @param context - Additional context information
	 * @returns Standardized error object
	 */
	async handleError(
		error: Error | string,
		context: ErrorContext = {}
	): Promise<ErrorDetails> {
		const errorDetails = this.normalizeError(error, context);
		
		// Log error with context
		await this.logError(errorDetails);
		
		// Store error in database for monitoring
		await this.storeError(errorDetails);
		
		return errorDetails;
	}

	/**
	 * Handle webhook-specific errors
	 * @param error - Error object or message
	 * @param webhookType - Type of webhook (e.g., "patient.created")
	 * @param payload - Webhook payload
	 * @returns Standardized error object
	 */
	async handleWebhookError(
		error: Error | string,
		webhookType: string,
		payload?: any
	): Promise<ErrorDetails> {
		const context: ErrorContext = {
			service: "webhook",
			webhookType,
			payloadSize: payload ? JSON.stringify(payload).length : 0,
		};

		// Add relevant IDs from payload
		if (payload) {
			if (payload.id) context.entityId = payload.id;
			if (payload.patient) context.patientId = payload.patient;
			if (payload.contactId) context.contactId = payload.contactId;
		}

		return this.handleError(error, context);
	}

	/**
	 * Handle sync-specific errors
	 * @param error - Error object or message
	 * @param syncType - Type of sync operation
	 * @param entityId - ID of entity being synced
	 * @param direction - Sync direction (e.g., "CC_TO_AP", "AP_TO_CC")
	 * @returns Standardized error object
	 */
	async handleSyncError(
		error: Error | string,
		syncType: string,
		entityId: string,
		direction: string
	): Promise<ErrorDetails> {
		const context: ErrorContext = {
			service: "sync",
			method: syncType,
			entityId,
			direction,
		};

		return this.handleError(error, context);
	}

	/**
	 * Handle API request errors
	 * @param error - Error object or message
	 * @param apiType - API type (e.g., "AP", "CC")
	 * @param endpoint - API endpoint
	 * @param method - HTTP method
	 * @returns Standardized error object
	 */
	async handleApiError(
		error: Error | string,
		apiType: string,
		endpoint: string,
		method: string
	): Promise<ErrorDetails> {
		const context: ErrorContext = {
			service: "api",
			apiType,
			endpoint,
			method,
		};

		return this.handleError(error, context);
	}

	/**
	 * Handle validation errors
	 * @param error - Error object or message
	 * @param validationType - Type of validation
	 * @param data - Data that failed validation
	 * @returns Standardized error object
	 */
	async handleValidationError(
		error: Error | string,
		validationType: string,
		data?: any
	): Promise<ErrorDetails> {
		const context: ErrorContext = {
			service: "validation",
			method: validationType,
			dataType: typeof data,
		};

		return this.handleError(error, context);
	}

	/**
	 * Create a retry wrapper for operations
	 * @param operation - Function to retry
	 * @param maxRetries - Maximum number of retries
	 * @param delayMs - Delay between retries in milliseconds
	 * @param context - Error context
	 * @returns Result of operation or throws final error
	 */
	async withRetry<T>(
		operation: () => Promise<T>,
		maxRetries: number = 3,
		delayMs: number = 1000,
		context: ErrorContext = {}
	): Promise<T> {
		let lastError: Error;

		for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
			try {
				return await operation();
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));

				if (attempt <= maxRetries) {
					logger.warn(`Operation failed, retrying (${attempt}/${maxRetries})`, {
						error: lastError.message,
						context,
						attempt,
						nextRetryIn: delayMs,
					});

					// Wait before retry
					await new Promise(resolve => setTimeout(resolve, delayMs));
					
					// Exponential backoff
					delayMs *= 2;
				}
			}
		}

		// All retries failed, handle final error
		await this.handleError(lastError!, {
			...context,
			retries: maxRetries,
			finalAttempt: true,
		});

		throw lastError!;
	}

	/**
	 * Normalize error into standard format
	 * @param error - Error object or message
	 * @param context - Additional context
	 * @returns Normalized error details
	 */
	private normalizeError(error: Error | string, context: ErrorContext): ErrorDetails {
		if (error instanceof Error) {
			return {
				message: error.message,
				stack: error.stack,
				code: (error as any).code,
				statusCode: (error as any).statusCode || (error as any).status,
				context,
				originalError: error,
			};
		}

		return {
			message: String(error),
			context,
		};
	}

	/**
	 * Log error with appropriate level
	 * @param errorDetails - Error details to log
	 */
	private async logError(errorDetails: ErrorDetails): Promise<void> {
		const logData = {
			message: errorDetails.message,
			stack: errorDetails.stack,
			code: errorDetails.code,
			statusCode: errorDetails.statusCode,
			context: errorDetails.context,
		};

		// Determine log level based on error type
		if (errorDetails.statusCode && errorDetails.statusCode >= 500) {
			logger.error("Server error occurred", logData);
		} else if (errorDetails.statusCode && errorDetails.statusCode >= 400) {
			logger.warn("Client error occurred", logData);
		} else if (errorDetails.context?.service === "validation") {
			logger.warn("Validation error occurred", logData);
		} else {
			logger.error("Unexpected error occurred", logData);
		}
	}

	/**
	 * Store error in database for monitoring and analysis
	 * @param errorDetails - Error details to store
	 */
	private async storeError(errorDetails: ErrorDetails): Promise<void> {
		try {
			await this.db.insert(dbSchema.errorLogs).values({
				message: errorDetails.message,
				stack: errorDetails.stack,
				type: errorDetails.context?.service || "unknown",
				data: {
					code: errorDetails.code,
					statusCode: errorDetails.statusCode,
					context: errorDetails.context,
				},
			});
		} catch (dbError) {
			// Don't let database errors prevent error handling
			logger.error("Failed to store error in database", {
				originalError: errorDetails.message,
				dbError: dbError instanceof Error ? dbError.message : String(dbError),
			});
		}
	}

	/**
	 * Get error statistics for monitoring
	 * @param timeframe - Timeframe in hours (default: 24)
	 * @returns Error statistics
	 */
	async getErrorStats(timeframe: number = 24): Promise<{
		total: number;
		byType: Record<string, number>;
		byService: Record<string, number>;
		recent: any[];
	}> {
		try {
			const since = new Date(Date.now() - timeframe * 60 * 60 * 1000);
			
			const errors = await this.db.query.errorLogs.findMany({
				where: (errorLogs, { gte }) => gte(errorLogs.createdAt, since),
				orderBy: (errorLogs, { desc }) => [desc(errorLogs.createdAt)],
				limit: 100,
			});

			const byType: Record<string, number> = {};
			const byService: Record<string, number> = {};

			errors.forEach(error => {
				byType[error.type] = (byType[error.type] || 0) + 1;
				
				const service = (error.data as any)?.context?.service || "unknown";
				byService[service] = (byService[service] || 0) + 1;
			});

			return {
				total: errors.length,
				byType,
				byService,
				recent: errors.slice(0, 10),
			};
		} catch (error) {
			logger.error("Failed to get error statistics", {
				error: error instanceof Error ? error.message : String(error),
			});
			
			return {
				total: 0,
				byType: {},
				byService: {},
				recent: [],
			};
		}
	}

	/**
	 * Check if error is retryable
	 * @param error - Error to check
	 * @returns True if error should be retried
	 */
	isRetryableError(error: Error): boolean {
		// Network errors
		if (error.message.includes("ECONNRESET") || 
			error.message.includes("ETIMEDOUT") ||
			error.message.includes("ENOTFOUND")) {
			return true;
		}

		// HTTP status codes that should be retried
		const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
		const statusCode = (error as any).statusCode || (error as any).status;
		
		return retryableStatusCodes.includes(statusCode);
	}
}

// Export singleton instance
export const errorHandler = new ErrorHandlingService();
