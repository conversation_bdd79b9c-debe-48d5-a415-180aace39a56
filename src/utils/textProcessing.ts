/**
 * Text processing utilities
 * Contains functions for cleaning, formatting, and manipulating text data
 */

/**
 * Remove HTML tags from a string
 * @param text - Text containing HTML tags
 * @returns Clean text without HTML tags
 */
export function removeHtmlTags(text: string): string {
	if (!text || typeof text !== "string") {
		return "";
	}
	return text.replace(/<[^>]*>/g, "");
}

/**
 * Reduce custom field value to fit field limits
 * Truncates long text and adds ellipsis if needed
 * @param values - Array of string values to join
 * @param maxLength - Maximum length (default: 255)
 * @param separator - Separator to use when joining (default: ", ")
 * @returns Truncated string with ellipsis if needed
 */
export function reduceCustomFieldValue(
	values: string[],
	maxLength: number = 255,
	separator: string = ", "
): string {
	if (!Array.isArray(values)) {
		return "";
	}

	const joined = values.filter(Boolean).join(separator);
	return joined.length > maxLength 
		? `${joined.substring(0, maxLength - 3)}...` 
		: joined;
}

/**
 * Clean and normalize text for comparison
 * @param text - Text to normalize
 * @returns Normalized text (lowercase, trimmed, no extra spaces)
 */
export function normalizeText(text: string): string {
	if (!text || typeof text !== "string") {
		return "";
	}
	return text.toLowerCase().trim().replace(/\s+/g, " ");
}

/**
 * Extract email addresses from text
 * @param text - Text to search for emails
 * @returns Array of email addresses found
 */
export function extractEmails(text: string): string[] {
	if (!text || typeof text !== "string") {
		return [];
	}
	
	const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
	return text.match(emailRegex) || [];
}

/**
 * Extract phone numbers from text
 * @param text - Text to search for phone numbers
 * @returns Array of phone numbers found
 */
export function extractPhoneNumbers(text: string): string[] {
	if (!text || typeof text !== "string") {
		return [];
	}
	
	// Matches various phone number formats
	const phoneRegex = /(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
	const matches = text.match(phoneRegex) || [];
	return matches.map(phone => phone.replace(/\D/g, "")); // Remove non-digits
}

/**
 * Capitalize first letter of each word
 * @param text - Text to capitalize
 * @returns Text with first letter of each word capitalized
 */
export function capitalizeWords(text: string): string {
	if (!text || typeof text !== "string") {
		return "";
	}
	
	return text.replace(/\b\w/g, (char) => char.toUpperCase());
}

/**
 * Generate a slug from text (URL-friendly string)
 * @param text - Text to convert to slug
 * @returns URL-friendly slug
 */
export function generateSlug(text: string): string {
	if (!text || typeof text !== "string") {
		return "";
	}
	
	return text
		.toLowerCase()
		.trim()
		.replace(/[^\w\s-]/g, "") // Remove special characters
		.replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
		.replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
}

/**
 * Truncate text to specified length with ellipsis
 * @param text - Text to truncate
 * @param maxLength - Maximum length
 * @param ellipsis - Ellipsis string (default: "...")
 * @returns Truncated text
 */
export function truncateText(
	text: string, 
	maxLength: number, 
	ellipsis: string = "..."
): string {
	if (!text || typeof text !== "string") {
		return "";
	}
	
	if (text.length <= maxLength) {
		return text;
	}
	
	return text.substring(0, maxLength - ellipsis.length) + ellipsis;
}

/**
 * Check if text contains any of the specified keywords
 * @param text - Text to search in
 * @param keywords - Array of keywords to search for
 * @param caseSensitive - Whether search should be case sensitive (default: false)
 * @returns True if any keyword is found
 */
export function containsKeywords(
	text: string, 
	keywords: string[], 
	caseSensitive: boolean = false
): boolean {
	if (!text || !Array.isArray(keywords)) {
		return false;
	}
	
	const searchText = caseSensitive ? text : text.toLowerCase();
	return keywords.some(keyword => {
		const searchKeyword = caseSensitive ? keyword : keyword.toLowerCase();
		return searchText.includes(searchKeyword);
	});
}

/**
 * Replace multiple spaces with single space and trim
 * @param text - Text to clean
 * @returns Cleaned text
 */
export function cleanWhitespace(text: string): string {
	if (!text || typeof text !== "string") {
		return "";
	}
	
	return text.replace(/\s+/g, " ").trim();
}

/**
 * Convert text to title case
 * @param text - Text to convert
 * @returns Text in title case
 */
export function toTitleCase(text: string): string {
	if (!text || typeof text !== "string") {
		return "";
	}
	
	return text.replace(/\w\S*/g, (txt) => 
		txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
	);
}

/**
 * Mask sensitive information in text (like email, phone)
 * @param text - Text containing sensitive info
 * @param maskChar - Character to use for masking (default: "*")
 * @returns Text with sensitive info masked
 */
export function maskSensitiveInfo(text: string, maskChar: string = "*"): string {
	if (!text || typeof text !== "string") {
		return "";
	}
	
	// Mask email addresses
	text = text.replace(
		/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
		(email) => {
			const [local, domain] = email.split("@");
			const maskedLocal = local.charAt(0) + maskChar.repeat(local.length - 2) + local.charAt(local.length - 1);
			return `${maskedLocal}@${domain}`;
		}
	);
	
	// Mask phone numbers
	text = text.replace(
		/(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g,
		(phone) => {
			const digits = phone.replace(/\D/g, "");
			return `${digits.substring(0, 3)}${maskChar.repeat(3)}${digits.substring(6)}`;
		}
	);
	
	return text;
}
