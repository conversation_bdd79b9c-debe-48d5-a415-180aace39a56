/**
 * Date and time utilities using date-fns
 * Provides consistent date/time handling across the application
 */

import {
	format,
	parseISO,
	isValid,
	addDays,
	addHours,
	addMinutes,
	subDays,
	subHours,
	subMinutes,
	startOfDay,
	endOfDay,
	startOfWeek,
	endOfWeek,
	startOfMonth,
	endOfMonth,
	differenceInDays,
	differenceInHours,
	differenceInMinutes,
	isBefore,
	isAfter,
	isSameDay,
	formatDistanceToNow,
} from "date-fns";
import { de } from "date-fns/locale";

/**
 * Default timezone for the application
 */
export const DEFAULT_TIMEZONE = "Europe/Berlin";

/**
 * Common date format patterns
 */
export const DATE_FORMATS = {
	ISO: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
	DATE_ONLY: "yyyy-MM-dd",
	TIME_ONLY: "HH:mm",
	DATETIME: "yyyy-MM-dd HH:mm",
	DISPLAY_DATE: "dd.MM.yyyy",
	DISPLAY_DATETIME: "dd.MM.yyyy HH:mm",
	DISPLAY_TIME: "HH:mm",
	GERMAN_FULL: "EEEE, dd. MMMM yyyy",
	GERMAN_SHORT: "dd.MM.yy",
} as const;

/**
 * Parse a date string or Date object into a Date
 * @param dateInput - Date string or Date object
 * @returns Date object or null if invalid
 */
export function parseDate(dateInput: string | Date | null | undefined): Date | null {
	if (!dateInput) {
		return null;
	}

	if (dateInput instanceof Date) {
		return isValid(dateInput) ? dateInput : null;
	}

	if (typeof dateInput === "string") {
		const parsed = parseISO(dateInput);
		return isValid(parsed) ? parsed : null;
	}

	return null;
}

/**
 * Format a date using the specified format
 * @param date - Date to format
 * @param formatString - Format string (default: DISPLAY_DATETIME)
 * @param locale - Locale to use (default: German)
 * @returns Formatted date string
 */
export function formatDate(
	date: string | Date | null | undefined,
	formatString: string = DATE_FORMATS.DISPLAY_DATETIME,
	locale = de
): string {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return "";
	}

	try {
		return format(parsedDate, formatString, { locale });
	} catch (error) {
		console.warn("Date formatting error:", error);
		return "";
	}
}

/**
 * Format date for German display
 * @param date - Date to format
 * @param includeTime - Whether to include time (default: true)
 * @returns German formatted date string
 */
export function formatGermanDate(
	date: string | Date | null | undefined,
	includeTime: boolean = true
): string {
	const formatString = includeTime 
		? DATE_FORMATS.DISPLAY_DATETIME 
		: DATE_FORMATS.DISPLAY_DATE;
	return formatDate(date, formatString, de);
}

/**
 * Format date for ISO string output
 * @param date - Date to format
 * @returns ISO formatted date string
 */
export function formatISODate(date: string | Date | null | undefined): string {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return "";
	}

	return parsedDate.toISOString();
}

/**
 * Get relative time description (e.g., "2 hours ago", "in 3 days")
 * @param date - Date to compare
 * @param baseDate - Base date to compare against (default: now)
 * @returns Relative time string in German
 */
export function getRelativeTime(
	date: string | Date | null | undefined,
	baseDate?: Date
): string {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return "";
	}

	try {
		return formatDistanceToNow(parsedDate, {
			addSuffix: true,
			locale: de,
		});
	} catch (error) {
		console.warn("Relative time formatting error:", error);
		return "";
	}
}

/**
 * Check if a date is in the past
 * @param date - Date to check
 * @returns True if date is in the past
 */
export function isInPast(date: string | Date | null | undefined): boolean {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return false;
	}

	return isBefore(parsedDate, new Date());
}

/**
 * Check if a date is in the future
 * @param date - Date to check
 * @returns True if date is in the future
 */
export function isInFuture(date: string | Date | null | undefined): boolean {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return false;
	}

	return isAfter(parsedDate, new Date());
}

/**
 * Check if two dates are on the same day
 * @param date1 - First date
 * @param date2 - Second date
 * @returns True if dates are on the same day
 */
export function isSameDate(
	date1: string | Date | null | undefined,
	date2: string | Date | null | undefined
): boolean {
	const parsed1 = parseDate(date1);
	const parsed2 = parseDate(date2);

	if (!parsed1 || !parsed2) {
		return false;
	}

	return isSameDay(parsed1, parsed2);
}

/**
 * Add time to a date
 * @param date - Base date
 * @param amount - Amount to add
 * @param unit - Time unit (days, hours, minutes)
 * @returns New date with added time
 */
export function addTime(
	date: string | Date | null | undefined,
	amount: number,
	unit: "days" | "hours" | "minutes"
): Date | null {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return null;
	}

	switch (unit) {
		case "days":
			return addDays(parsedDate, amount);
		case "hours":
			return addHours(parsedDate, amount);
		case "minutes":
			return addMinutes(parsedDate, amount);
		default:
			return parsedDate;
	}
}

/**
 * Subtract time from a date
 * @param date - Base date
 * @param amount - Amount to subtract
 * @param unit - Time unit (days, hours, minutes)
 * @returns New date with subtracted time
 */
export function subtractTime(
	date: string | Date | null | undefined,
	amount: number,
	unit: "days" | "hours" | "minutes"
): Date | null {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return null;
	}

	switch (unit) {
		case "days":
			return subDays(parsedDate, amount);
		case "hours":
			return subHours(parsedDate, amount);
		case "minutes":
			return subMinutes(parsedDate, amount);
		default:
			return parsedDate;
	}
}

/**
 * Get the difference between two dates
 * @param date1 - First date
 * @param date2 - Second date
 * @param unit - Unit to return difference in
 * @returns Difference in specified unit
 */
export function getDateDifference(
	date1: string | Date | null | undefined,
	date2: string | Date | null | undefined,
	unit: "days" | "hours" | "minutes"
): number {
	const parsed1 = parseDate(date1);
	const parsed2 = parseDate(date2);

	if (!parsed1 || !parsed2) {
		return 0;
	}

	switch (unit) {
		case "days":
			return differenceInDays(parsed1, parsed2);
		case "hours":
			return differenceInHours(parsed1, parsed2);
		case "minutes":
			return differenceInMinutes(parsed1, parsed2);
		default:
			return 0;
	}
}

/**
 * Get start and end of day for a date
 * @param date - Date to get boundaries for
 * @returns Object with start and end of day
 */
export function getDayBoundaries(date: string | Date | null | undefined): {
	start: Date | null;
	end: Date | null;
} {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return { start: null, end: null };
	}

	return {
		start: startOfDay(parsedDate),
		end: endOfDay(parsedDate),
	};
}

/**
 * Get start and end of week for a date
 * @param date - Date to get boundaries for
 * @returns Object with start and end of week
 */
export function getWeekBoundaries(date: string | Date | null | undefined): {
	start: Date | null;
	end: Date | null;
} {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return { start: null, end: null };
	}

	return {
		start: startOfWeek(parsedDate, { weekStartsOn: 1 }), // Monday
		end: endOfWeek(parsedDate, { weekStartsOn: 1 }),
	};
}

/**
 * Get start and end of month for a date
 * @param date - Date to get boundaries for
 * @returns Object with start and end of month
 */
export function getMonthBoundaries(date: string | Date | null | undefined): {
	start: Date | null;
	end: Date | null;
} {
	const parsedDate = parseDate(date);
	if (!parsedDate) {
		return { start: null, end: null };
	}

	return {
		start: startOfMonth(parsedDate),
		end: endOfMonth(parsedDate),
	};
}

/**
 * Create a date range string for display
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Formatted date range string
 */
export function formatDateRange(
	startDate: string | Date | null | undefined,
	endDate: string | Date | null | undefined
): string {
	const start = parseDate(startDate);
	const end = parseDate(endDate);

	if (!start && !end) {
		return "";
	}

	if (!start) {
		return `bis ${formatGermanDate(end)}`;
	}

	if (!end) {
		return `ab ${formatGermanDate(start)}`;
	}

	if (isSameDate(start, end)) {
		return `${formatGermanDate(start, false)} ${formatDate(start, DATE_FORMATS.TIME_ONLY)} - ${formatDate(end, DATE_FORMATS.TIME_ONLY)}`;
	}

	return `${formatGermanDate(start)} - ${formatGermanDate(end)}`;
}
