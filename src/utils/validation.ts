/**
 * Data validation utilities
 * Contains functions for validating various types of data
 */

/**
 * Validate email address format
 * @param email - Email address to validate
 * @returns True if email is valid
 */
export function isValidEmail(email: string): boolean {
	if (!email || typeof email !== "string") {
		return false;
	}
	
	const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$/;
	return emailRegex.test(email.trim());
}

/**
 * Validate phone number format
 * @param phone - Phone number to validate
 * @returns True if phone number is valid
 */
export function isValidPhone(phone: string): boolean {
	if (!phone || typeof phone !== "string") {
		return false;
	}
	
	// Remove all non-digit characters
	const digits = phone.replace(/\D/g, "");
	
	// Check if it's a valid length (10-15 digits)
	return digits.length >= 10 && digits.length <= 15;
}

/**
 * Validate date string format
 * @param dateString - Date string to validate
 * @returns True if date string is valid
 */
export function isValidDate(dateString: string): boolean {
	if (!dateString || typeof dateString !== "string") {
		return false;
	}
	
	const date = new Date(dateString);
	return !isNaN(date.getTime());
}

/**
 * Validate URL format
 * @param url - URL to validate
 * @returns True if URL is valid
 */
export function isValidUrl(url: string): boolean {
	if (!url || typeof url !== "string") {
		return false;
	}
	
	try {
		new URL(url);
		return true;
	} catch {
		return false;
	}
}

/**
 * Validate that a value is not empty
 * @param value - Value to check
 * @returns True if value is not empty
 */
export function isNotEmpty(value: unknown): boolean {
	if (value === null || value === undefined) {
		return false;
	}
	
	if (typeof value === "string") {
		return value.trim().length > 0;
	}
	
	if (Array.isArray(value)) {
		return value.length > 0;
	}
	
	if (typeof value === "object") {
		return Object.keys(value).length > 0;
	}
	
	return true;
}

/**
 * Validate patient data structure
 * @param patient - Patient data to validate
 * @returns Validation result with errors
 */
export function validatePatientData(patient: any): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];
	
	if (!patient) {
		errors.push("Patient data is required");
		return { isValid: false, errors };
	}
	
	// Check required fields
	if (!patient.firstName || typeof patient.firstName !== "string") {
		errors.push("First name is required and must be a string");
	}
	
	if (!patient.lastName || typeof patient.lastName !== "string") {
		errors.push("Last name is required and must be a string");
	}
	
	// Check email if provided
	if (patient.email && !isValidEmail(patient.email)) {
		errors.push("Email format is invalid");
	}
	
	// Check phone if provided
	if (patient.phone && !isValidPhone(patient.phone)) {
		errors.push("Phone number format is invalid");
	}
	
	// At least one contact method required
	if (!patient.email && !patient.phone) {
		errors.push("Either email or phone number is required");
	}
	
	// Check date of birth if provided
	if (patient.dateOfBirth && !isValidDate(patient.dateOfBirth)) {
		errors.push("Date of birth format is invalid");
	}
	
	return {
		isValid: errors.length === 0,
		errors
	};
}

/**
 * Validate appointment data structure
 * @param appointment - Appointment data to validate
 * @returns Validation result with errors
 */
export function validateAppointmentData(appointment: any): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];
	
	if (!appointment) {
		errors.push("Appointment data is required");
		return { isValid: false, errors };
	}
	
	// Check start time
	if (!appointment.startTime || !isValidDate(appointment.startTime)) {
		errors.push("Valid start time is required");
	}
	
	// Check end time
	if (!appointment.endTime || !isValidDate(appointment.endTime)) {
		errors.push("Valid end time is required");
	}
	
	// Check that end time is after start time
	if (appointment.startTime && appointment.endTime) {
		const start = new Date(appointment.startTime);
		const end = new Date(appointment.endTime);
		
		if (end <= start) {
			errors.push("End time must be after start time");
		}
	}
	
	// Check patient/contact ID
	if (!appointment.patientId && !appointment.contactId) {
		errors.push("Patient ID or Contact ID is required");
	}
	
	return {
		isValid: errors.length === 0,
		errors
	};
}

/**
 * Validate custom field data
 * @param customField - Custom field data to validate
 * @returns Validation result with errors
 */
export function validateCustomFieldData(customField: any): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];
	
	if (!customField) {
		errors.push("Custom field data is required");
		return { isValid: false, errors };
	}
	
	// Check field name
	if (!customField.name || typeof customField.name !== "string") {
		errors.push("Field name is required and must be a string");
	}
	
	// Check field value
	if (customField.value === null || customField.value === undefined) {
		errors.push("Field value is required");
	}
	
	// Check field ID if provided
	if (customField.id && typeof customField.id !== "string" && typeof customField.id !== "number") {
		errors.push("Field ID must be a string or number");
	}
	
	return {
		isValid: errors.length === 0,
		errors
	};
}

/**
 * Validate webhook payload structure
 * @param payload - Webhook payload to validate
 * @param requiredFields - Array of required field names
 * @returns Validation result with errors
 */
export function validateWebhookPayload(
	payload: any,
	requiredFields: string[] = []
): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];
	
	if (!payload || typeof payload !== "object") {
		errors.push("Payload must be an object");
		return { isValid: false, errors };
	}
	
	// Check required fields
	for (const field of requiredFields) {
		if (!(field in payload) || payload[field] === null || payload[field] === undefined) {
			errors.push(`Required field '${field}' is missing or null`);
		}
	}
	
	return {
		isValid: errors.length === 0,
		errors
	};
}

/**
 * Sanitize string input to prevent injection attacks
 * @param input - String input to sanitize
 * @returns Sanitized string
 */
export function sanitizeInput(input: string): string {
	if (!input || typeof input !== "string") {
		return "";
	}
	
	return input
		.replace(/[<>]/g, "") // Remove angle brackets
		.replace(/javascript:/gi, "") // Remove javascript: protocol
		.replace(/on\w+=/gi, "") // Remove event handlers
		.trim();
}

/**
 * Check if a value is a valid UUID
 * @param value - Value to check
 * @returns True if value is a valid UUID
 */
export function isValidUUID(value: string): boolean {
	if (!value || typeof value !== "string") {
		return false;
	}
	
	const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
	return uuidRegex.test(value);
}

/**
 * Check if a value is a positive integer
 * @param value - Value to check
 * @returns True if value is a positive integer
 */
export function isPositiveInteger(value: any): boolean {
	return Number.isInteger(value) && value > 0;
}
