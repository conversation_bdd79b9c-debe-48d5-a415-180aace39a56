import { Hono } from "hono";
import { logger } from "@/utils";
import { PatientWebhookHandler } from "./handlers/PatientWebhookHandler";
import { AppointmentWebhookHandler } from "./handlers/AppointmentWebhookHandler";
import { InvoiceWebhookHandler } from "./handlers/InvoiceWebhookHandler";
import { PaymentWebhookHandler } from "./handlers/PaymentWebhookHandler";

export const webhookRoutes = new Hono<Env>();

// Middleware for webhook authentication
webhookRoutes.use("*", async (c, next) => {
	const authHeader = c.req.header("Authorization");
	const expectedToken = c.env.WEBHOOK_SECRET;

	if (!authHeader || !expectedToken) {
		logger.warn("Missing webhook authentication", {
			hasAuth: !!authHeader,
			hasSecret: !!expectedToken,
			path: c.req.path,
		});
		return c.json({ error: "Unauthorized" }, 401);
	}

	const token = authHeader.replace("Bearer ", "");
	if (token !== expectedToken) {
		logger.warn("Invalid webhook token", {
			path: c.req.path,
			providedToken: token.substring(0, 8) + "...",
		});
		return c.json({ error: "Unauthorized" }, 401);
	}

	await next();
});

// CliniCore webhook endpoints
webhookRoutes.post("/cc/patient/created", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received CC patient created webhook", {
			patientId: payload.id,
			email: payload.email,
		});

		const handler = new PatientWebhookHandler();
		await handler.handlePatientCreated(payload);

		return c.json({ success: true, message: "Patient created successfully" });
	} catch (error) {
		logger.error("Error processing CC patient created webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/cc/patient/updated", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received CC patient updated webhook", {
			patientId: payload.id,
			email: payload.email,
		});

		const handler = new PatientWebhookHandler();
		await handler.handlePatientUpdated(payload);

		return c.json({ success: true, message: "Patient updated successfully" });
	} catch (error) {
		logger.error("Error processing CC patient updated webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/cc/appointment/created", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received CC appointment created webhook", {
			appointmentId: payload.id,
			patientId: payload.patients?.[0],
		});

		const handler = new AppointmentWebhookHandler();
		await handler.handleAppointmentCreated(payload);

		return c.json({ success: true, message: "Appointment created successfully" });
	} catch (error) {
		logger.error("Error processing CC appointment created webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/cc/appointment/updated", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received CC appointment updated webhook", {
			appointmentId: payload.id,
			patientId: payload.patients?.[0],
		});

		const handler = new AppointmentWebhookHandler();
		await handler.handleAppointmentUpdated(payload);

		return c.json({ success: true, message: "Appointment updated successfully" });
	} catch (error) {
		logger.error("Error processing CC appointment updated webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/cc/appointment/deleted", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received CC appointment deleted webhook", {
			appointmentId: payload.id,
		});

		const handler = new AppointmentWebhookHandler();
		await handler.handleAppointmentDeleted(payload);

		return c.json({ success: true, message: "Appointment deleted successfully" });
	} catch (error) {
		logger.error("Error processing CC appointment deleted webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/cc/invoice/created", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received CC invoice created webhook", {
			invoiceId: payload.id,
			patientId: payload.patient,
		});

		const handler = new InvoiceWebhookHandler();
		await handler.handleInvoiceCreated(payload);

		return c.json({ success: true, message: "Invoice processed successfully" });
	} catch (error) {
		logger.error("Error processing CC invoice created webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/cc/payment/created", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received CC payment created webhook", {
			paymentId: payload.id,
			patientId: payload.patient,
		});

		const handler = new PaymentWebhookHandler();
		await handler.handlePaymentCreated(payload);

		return c.json({ success: true, message: "Payment processed successfully" });
	} catch (error) {
		logger.error("Error processing CC payment created webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

// AutoPatient webhook endpoints (for reverse sync)
webhookRoutes.post("/ap/contact/created", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received AP contact created webhook", {
			contactId: payload.id,
			email: payload.email,
		});

		const handler = new PatientWebhookHandler();
		await handler.handleAPContactCreated(payload);

		return c.json({ success: true, message: "Contact processed successfully" });
	} catch (error) {
		logger.error("Error processing AP contact created webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/ap/contact/updated", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received AP contact updated webhook", {
			contactId: payload.id,
			email: payload.email,
		});

		const handler = new PatientWebhookHandler();
		await handler.handleAPContactUpdated(payload);

		return c.json({ success: true, message: "Contact updated successfully" });
	} catch (error) {
		logger.error("Error processing AP contact updated webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/ap/appointment/created", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received AP appointment created webhook", {
			appointmentId: payload.id,
			contactId: payload.contactId,
		});

		const handler = new AppointmentWebhookHandler();
		await handler.handleAPAppointmentCreated(payload);

		return c.json({ success: true, message: "Appointment processed successfully" });
	} catch (error) {
		logger.error("Error processing AP appointment created webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});

webhookRoutes.post("/ap/appointment/updated", async (c) => {
	try {
		const payload = await c.req.json();
		logger.info("Received AP appointment updated webhook", {
			appointmentId: payload.id,
			contactId: payload.contactId,
		});

		const handler = new AppointmentWebhookHandler();
		await handler.handleAPAppointmentUpdated(payload);

		return c.json({ success: true, message: "Appointment updated successfully" });
	} catch (error) {
		logger.error("Error processing AP appointment updated webhook", {
			error: error.message,
			stack: error.stack,
		});
		return c.json({ error: "Internal server error" }, 500);
	}
});
