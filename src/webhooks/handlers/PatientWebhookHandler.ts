import type { GetCCPatientType, GetAPContactType } from "@/lib";
import { PatientSyncService } from "@/services/PatientSyncService";
import { logger } from "@/utils";
import { LocalPatient } from "@/lib/base/LocalPatient";

export class PatientWebhookHandler extends LocalPatient {
	private syncService: PatientSyncService;

	constructor() {
		super();
		this.syncService = new PatientSyncService();
	}

	/**
	 * Handle CliniCore patient created webhook
	 * Creates or updates patient in local database and syncs to AutoPatient
	 */
	async handlePatientCreated(payload: GetCCPatientType): Promise<void> {
		try {
			logger.info("Processing CC patient created", {
				patientId: payload.id,
				email: payload.email,
				phone: payload.phoneMobile,
			});

			// Check if patient already exists locally
			let localPatient = await this.getPatientByCCId(payload.id);

			if (localPatient?.apId) {
				logger.info("Patient already exists in AP, skipping creation", {
					patientId: payload.id,
					apId: localPatient.apId,
				});
				return;
			}

			// Upsert patient to local database
			await this.upsertLocalFromCC(payload);

			// Sync to AutoPatient
			await this.syncService.syncCCPatientToAP(this.patient!);

			logger.info("CC patient created and synced successfully", {
				patientId: payload.id,
				localId: this.patient?.id,
				apId: this.patient?.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC patient created", {
				patientId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle CliniCore patient updated webhook
	 * Updates patient in local database and syncs changes to AutoPatient
	 */
	async handlePatientUpdated(payload: GetCCPatientType): Promise<void> {
		try {
			logger.info("Processing CC patient updated", {
				patientId: payload.id,
				email: payload.email,
				phone: payload.phoneMobile,
			});

			// Get existing patient or create if not exists
			let localPatient = await this.getPatientByCCId(payload.id);

			if (!localPatient) {
				logger.info("Patient not found locally, creating new", {
					patientId: payload.id,
				});
				await this.upsertLocalFromCC(payload);
			} else {
				// Update local patient data
				await this.db
					.update(this.dbSchema.patient)
					.set({
						ccData: payload,
						ccUpdatedAt: new Date(payload.updatedAt),
						email: payload.email,
						phone: payload.phoneMobile,
					})
					.where(this.db.eq(this.dbSchema.patient.ccId, payload.id));

				await this.refresh();
			}

			// Sync to AutoPatient if patient exists there
			if (this.patient?.apId) {
				await this.syncService.syncCCPatientToAP(this.patient);
			} else {
				// Create in AutoPatient if doesn't exist
				await this.syncService.syncCCPatientToAP(this.patient!);
			}

			logger.info("CC patient updated and synced successfully", {
				patientId: payload.id,
				localId: this.patient?.id,
				apId: this.patient?.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC patient updated", {
				patientId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle AutoPatient contact created webhook
	 * Creates or updates patient in local database and syncs to CliniCore
	 */
	async handleAPContactCreated(payload: GetAPContactType): Promise<void> {
		try {
			logger.info("Processing AP contact created", {
				contactId: payload.id,
				email: payload.email,
				phone: payload.phone,
			});

			// Check if contact already exists locally
			let localPatient = await this.getPatientByAPId(payload.id);

			if (localPatient?.ccId) {
				logger.info("Contact already exists in CC, skipping creation", {
					contactId: payload.id,
					ccId: localPatient.ccId,
				});
				return;
			}

			// Upsert contact to local database
			await this.upsertLocalFromAP(payload);

			// Sync to CliniCore if this is a new contact from AP
			if (payload.source !== "cc") {
				await this.syncService.syncAPContactToCC(this.patient!);
			}

			logger.info("AP contact created and synced successfully", {
				contactId: payload.id,
				localId: this.patient?.id,
				ccId: this.patient?.ccId,
			});
		} catch (error) {
			logger.error("Failed to process AP contact created", {
				contactId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle AutoPatient contact updated webhook
	 * Updates patient in local database and syncs changes to CliniCore
	 */
	async handleAPContactUpdated(payload: GetAPContactType): Promise<void> {
		try {
			logger.info("Processing AP contact updated", {
				contactId: payload.id,
				email: payload.email,
				phone: payload.phone,
			});

			// Get existing patient or create if not exists
			let localPatient = await this.getPatientByAPId(payload.id);

			if (!localPatient) {
				logger.info("Contact not found locally, creating new", {
					contactId: payload.id,
				});
				await this.upsertLocalFromAP(payload);
			} else {
				// Update local patient data
				await this.db
					.update(this.dbSchema.patient)
					.set({
						apData: payload,
						apUpdatedAt: new Date(payload.dateUpdated || new Date()),
						email: payload.email,
						phone: payload.phone,
					})
					.where(this.db.eq(this.dbSchema.patient.apId, payload.id));

				await this.refresh();
			}

			// Sync to CliniCore if patient exists there and wasn't originally from CC
			if (this.patient?.ccId && payload.source !== "cc") {
				await this.syncService.syncAPContactToCC(this.patient);
			} else if (!this.patient?.ccId && payload.source !== "cc") {
				// Create in CliniCore if doesn't exist
				await this.syncService.syncAPContactToCC(this.patient!);
			}

			logger.info("AP contact updated and synced successfully", {
				contactId: payload.id,
				localId: this.patient?.id,
				ccId: this.patient?.ccId,
			});
		} catch (error) {
			logger.error("Failed to process AP contact updated", {
				contactId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Validate patient data before processing
	 */
	private validatePatientData(data: GetCCPatientType | GetAPContactType): boolean {
		if ("id" in data && typeof data.id === "number") {
			// CC Patient validation
			const ccData = data as GetCCPatientType;
			return !!(ccData.email || ccData.phoneMobile);
		} else if ("id" in data && typeof data.id === "string") {
			// AP Contact validation
			const apData = data as GetAPContactType;
			return !!(apData.email || apData.phone);
		}
		return false;
	}
}
