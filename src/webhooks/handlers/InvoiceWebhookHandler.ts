import type { GetInvoiceType } from "@libCC/CCTypes";
import { PatientSyncService } from "@/services/PatientSyncService";
import { InvoicePaymentSyncService } from "@/services/InvoicePaymentSyncService";
import { logger } from "@/utils";

export class InvoiceWebhookHandler {
	private patientSyncService: PatientSyncService;
	private invoicePaymentService: InvoicePaymentSyncService;

	constructor() {
		this.patientSyncService = new PatientSyncService();
		this.invoicePaymentService = new InvoicePaymentSyncService();
	}

	/**
	 * Handle CliniCore invoice created webhook
	 * Processes invoice data and syncs to AutoPatient custom fields
	 */
	async handleInvoiceCreated(payload: GetInvoiceType): Promise<void> {
		try {
			logger.info("Processing CC invoice created", {
				invoiceId: payload.id,
				patientId: payload.patient,
				status: payload.status,
				gross: payload.positions?.reduce((sum, pos) => sum + pos.gross, 0) || 0,
			});

			// Ensure patient exists locally and in AP
			const localPatient = await this.patientSyncService.findOrCreatePatientByCCId(payload.patient);

			if (!localPatient.apId) {
				logger.warn("Patient doesn't have AP ID, cannot sync invoice data", {
					invoiceId: payload.id,
					patientId: payload.patient,
					localPatientId: localPatient.id,
				});
				return;
			}

			// Sync invoice and payment data to AP custom fields
			await this.invoicePaymentService.syncInvoicePayments(localPatient);

			logger.info("CC invoice processed and synced successfully", {
				invoiceId: payload.id,
				patientId: payload.patient,
				localPatientId: localPatient.id,
				apContactId: localPatient.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC invoice created", {
				invoiceId: payload.id,
				patientId: payload.patient,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle CliniCore invoice updated webhook
	 * Updates invoice data and syncs changes to AutoPatient custom fields
	 */
	async handleInvoiceUpdated(payload: GetInvoiceType): Promise<void> {
		try {
			logger.info("Processing CC invoice updated", {
				invoiceId: payload.id,
				patientId: payload.patient,
				status: payload.status,
				gross: payload.positions?.reduce((sum, pos) => sum + pos.gross, 0) || 0,
			});

			// Get existing patient
			const localPatient = await this.patientSyncService.getPatientByCCId(payload.patient);

			if (!localPatient) {
				logger.warn("Patient not found locally for invoice update", {
					invoiceId: payload.id,
					patientId: payload.patient,
				});
				return;
			}

			if (!localPatient.apId) {
				logger.warn("Patient doesn't have AP ID, cannot sync invoice data", {
					invoiceId: payload.id,
					patientId: payload.patient,
					localPatientId: localPatient.id,
				});
				return;
			}

			// Sync updated invoice and payment data to AP custom fields
			await this.invoicePaymentService.syncInvoicePayments(localPatient);

			logger.info("CC invoice updated and synced successfully", {
				invoiceId: payload.id,
				patientId: payload.patient,
				localPatientId: localPatient.id,
				apContactId: localPatient.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC invoice updated", {
				invoiceId: payload.id,
				patientId: payload.patient,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle CliniCore invoice deleted webhook
	 * Removes invoice data and updates AutoPatient custom fields
	 */
	async handleInvoiceDeleted(payload: { id: number; patient: number }): Promise<void> {
		try {
			logger.info("Processing CC invoice deleted", {
				invoiceId: payload.id,
				patientId: payload.patient,
			});

			// Get existing patient
			const localPatient = await this.patientSyncService.getPatientByCCId(payload.patient);

			if (!localPatient) {
				logger.warn("Patient not found locally for invoice deletion", {
					invoiceId: payload.id,
					patientId: payload.patient,
				});
				return;
			}

			if (!localPatient.apId) {
				logger.warn("Patient doesn't have AP ID, cannot sync invoice deletion", {
					invoiceId: payload.id,
					patientId: payload.patient,
					localPatientId: localPatient.id,
				});
				return;
			}

			// Re-sync invoice and payment data to reflect deletion
			await this.invoicePaymentService.syncInvoicePayments(localPatient);

			logger.info("CC invoice deleted and synced successfully", {
				invoiceId: payload.id,
				patientId: payload.patient,
				localPatientId: localPatient.id,
				apContactId: localPatient.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC invoice deleted", {
				invoiceId: payload.id,
				patientId: payload.patient,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Validate invoice data before processing
	 */
	private validateInvoiceData(data: GetInvoiceType): boolean {
		return !!(data.id && data.patient && data.positions);
	}
}
