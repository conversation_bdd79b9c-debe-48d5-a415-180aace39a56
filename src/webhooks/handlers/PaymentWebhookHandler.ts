import type { GetPaymentType } from "@libCC/CCTypes";
import { PatientSyncService } from "@/services/PatientSyncService";
import { InvoicePaymentSyncService } from "@/services/InvoicePaymentSyncService";
import { logger } from "@/utils";

export class PaymentWebhookHandler {
	private patientSyncService: PatientSyncService;
	private invoicePaymentService: InvoicePaymentSyncService;

	constructor() {
		this.patientSyncService = new PatientSyncService();
		this.invoicePaymentService = new InvoicePaymentSyncService();
	}

	/**
	 * Handle CliniCore payment created webhook
	 * Processes payment data and syncs to AutoPatient custom fields
	 */
	async handlePaymentCreated(payload: GetPaymentType): Promise<void> {
		try {
			logger.info("Processing CC payment created", {
				paymentId: payload.id,
				patientId: payload.patient,
				gross: payload.gross,
				status: payload.canceled ? "canceled" : "success",
			});

			// Ensure patient exists locally and in AP
			const localPatient = await this.patientSyncService.findOrCreatePatientByCCId(payload.patient);

			if (!localPatient.apId) {
				logger.warn("Patient doesn't have AP ID, cannot sync payment data", {
					paymentId: payload.id,
					patientId: payload.patient,
					localPatientId: localPatient.id,
				});
				return;
			}

			// Sync invoice and payment data to AP custom fields
			await this.invoicePaymentService.syncInvoicePayments(localPatient);

			logger.info("CC payment processed and synced successfully", {
				paymentId: payload.id,
				patientId: payload.patient,
				localPatientId: localPatient.id,
				apContactId: localPatient.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC payment created", {
				paymentId: payload.id,
				patientId: payload.patient,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle CliniCore payment updated webhook
	 * Updates payment data and syncs changes to AutoPatient custom fields
	 */
	async handlePaymentUpdated(payload: GetPaymentType): Promise<void> {
		try {
			logger.info("Processing CC payment updated", {
				paymentId: payload.id,
				patientId: payload.patient,
				gross: payload.gross,
				status: payload.canceled ? "canceled" : "success",
			});

			// Get existing patient
			const localPatient = await this.patientSyncService.getPatientByCCId(payload.patient);

			if (!localPatient) {
				logger.warn("Patient not found locally for payment update", {
					paymentId: payload.id,
					patientId: payload.patient,
				});
				return;
			}

			if (!localPatient.apId) {
				logger.warn("Patient doesn't have AP ID, cannot sync payment data", {
					paymentId: payload.id,
					patientId: payload.patient,
					localPatientId: localPatient.id,
				});
				return;
			}

			// Sync updated invoice and payment data to AP custom fields
			await this.invoicePaymentService.syncInvoicePayments(localPatient);

			logger.info("CC payment updated and synced successfully", {
				paymentId: payload.id,
				patientId: payload.patient,
				localPatientId: localPatient.id,
				apContactId: localPatient.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC payment updated", {
				paymentId: payload.id,
				patientId: payload.patient,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle CliniCore payment deleted webhook
	 * Removes payment data and updates AutoPatient custom fields
	 */
	async handlePaymentDeleted(payload: { id: number; patient: number }): Promise<void> {
		try {
			logger.info("Processing CC payment deleted", {
				paymentId: payload.id,
				patientId: payload.patient,
			});

			// Get existing patient
			const localPatient = await this.patientSyncService.getPatientByCCId(payload.patient);

			if (!localPatient) {
				logger.warn("Patient not found locally for payment deletion", {
					paymentId: payload.id,
					patientId: payload.patient,
				});
				return;
			}

			if (!localPatient.apId) {
				logger.warn("Patient doesn't have AP ID, cannot sync payment deletion", {
					paymentId: payload.id,
					patientId: payload.patient,
					localPatientId: localPatient.id,
				});
				return;
			}

			// Re-sync invoice and payment data to reflect deletion
			await this.invoicePaymentService.syncInvoicePayments(localPatient);

			logger.info("CC payment deleted and synced successfully", {
				paymentId: payload.id,
				patientId: payload.patient,
				localPatientId: localPatient.id,
				apContactId: localPatient.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC payment deleted", {
				paymentId: payload.id,
				patientId: payload.patient,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle CliniCore payment reversed webhook
	 * Processes payment reversal and updates AutoPatient custom fields
	 */
	async handlePaymentReversed(payload: GetPaymentType): Promise<void> {
		try {
			logger.info("Processing CC payment reversed", {
				paymentId: payload.id,
				patientId: payload.patient,
				gross: payload.gross,
				reversedBy: payload.reversedBy,
			});

			// Get existing patient
			const localPatient = await this.patientSyncService.getPatientByCCId(payload.patient);

			if (!localPatient) {
				logger.warn("Patient not found locally for payment reversal", {
					paymentId: payload.id,
					patientId: payload.patient,
				});
				return;
			}

			if (!localPatient.apId) {
				logger.warn("Patient doesn't have AP ID, cannot sync payment reversal", {
					paymentId: payload.id,
					patientId: payload.patient,
					localPatientId: localPatient.id,
				});
				return;
			}

			// Re-sync invoice and payment data to reflect reversal
			await this.invoicePaymentService.syncInvoicePayments(localPatient);

			logger.info("CC payment reversal processed and synced successfully", {
				paymentId: payload.id,
				patientId: payload.patient,
				localPatientId: localPatient.id,
				apContactId: localPatient.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC payment reversed", {
				paymentId: payload.id,
				patientId: payload.patient,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Validate payment data before processing
	 */
	private validatePaymentData(data: GetPaymentType): boolean {
		return !!(data.id && data.patient && typeof data.gross === "number");
	}
}
