import type { GetCCAppointmentType, GetAPAppointmentType } from "@/lib";
import { AppointmentSyncService } from "@/services/AppointmentSyncService";
import { PatientSyncService } from "@/services/PatientSyncService";
import { logger } from "@/utils";
import { LocalAppointment } from "@/lib/base/LocalAppointment";

export class AppointmentWebhookHandler extends LocalAppointment {
	private appointmentSyncService: AppointmentSyncService;
	private patientSyncService: PatientSyncService;

	constructor() {
		super();
		this.appointmentSyncService = new AppointmentSyncService();
		this.patientSyncService = new PatientSyncService();
	}

	/**
	 * Handle CliniCore appointment created webhook
	 */
	async handleAppointmentCreated(payload: GetCCAppointmentType): Promise<void> {
		try {
			logger.info("Processing CC appointment created", {
				appointmentId: payload.id,
				patientId: payload.patients?.[0],
				startsAt: payload.startsAt,
			});

			// Check if appointment already exists locally
			let localAppointment = await this.getAppointmentByCCId(payload.id);

			if (localAppointment?.apId) {
				logger.info("Appointment already exists in AP, skipping creation", {
					appointmentId: payload.id,
					apId: localAppointment.apId,
				});
				return;
			}

			// Ensure patient exists locally and in AP
			if (!payload.patients || payload.patients.length === 0) {
				throw new Error("No patients associated with appointment");
			}

			const patientId = payload.patients[0];
			const localPatient = await this.patientSyncService.findOrCreatePatientByCCId(patientId);

			// Create or update local appointment
			if (!localAppointment) {
				await this.upsertLocalFromCC(payload, localPatient.id);
			} else {
				// Update existing appointment
				await this.db
					.update(this.dbSchema.appointment)
					.set({
						ccData: payload,
						ccUpdatedAt: new Date(payload.updatedAt || new Date()),
						patientId: localPatient.id,
					})
					.where(this.db.eq(this.dbSchema.appointment.ccId, payload.id));

				await this.refresh();
			}

			// Sync to AutoPatient
			await this.appointmentSyncService.syncCCAppointmentToAP(this.appointment!);

			logger.info("CC appointment created and synced successfully", {
				appointmentId: payload.id,
				localId: this.appointment?.id,
				apId: this.appointment?.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC appointment created", {
				appointmentId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle CliniCore appointment updated webhook
	 */
	async handleAppointmentUpdated(payload: GetCCAppointmentType): Promise<void> {
		try {
			logger.info("Processing CC appointment updated", {
				appointmentId: payload.id,
				patientId: payload.patients?.[0],
				startsAt: payload.startsAt,
			});

			// Get existing appointment or create if not exists
			let localAppointment = await this.getAppointmentByCCId(payload.id);

			if (!localAppointment) {
				logger.info("Appointment not found locally, creating new", {
					appointmentId: payload.id,
				});
				
				// Ensure patient exists
				if (!payload.patients || payload.patients.length === 0) {
					throw new Error("No patients associated with appointment");
				}

				const patientId = payload.patients[0];
				const localPatient = await this.patientSyncService.findOrCreatePatientByCCId(patientId);
				await this.upsertLocalFromCC(payload, localPatient.id);
			} else {
				// Update existing appointment
				await this.db
					.update(this.dbSchema.appointment)
					.set({
						ccData: payload,
						ccUpdatedAt: new Date(payload.updatedAt || new Date()),
					})
					.where(this.db.eq(this.dbSchema.appointment.ccId, payload.id));

				await this.refresh();
			}

			// Sync to AutoPatient
			await this.appointmentSyncService.syncCCAppointmentToAP(this.appointment!);

			logger.info("CC appointment updated and synced successfully", {
				appointmentId: payload.id,
				localId: this.appointment?.id,
				apId: this.appointment?.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC appointment updated", {
				appointmentId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle CliniCore appointment deleted webhook
	 */
	async handleAppointmentDeleted(payload: { id: number }): Promise<void> {
		try {
			logger.info("Processing CC appointment deleted", {
				appointmentId: payload.id,
			});

			// Get existing appointment
			const localAppointment = await this.getAppointmentByCCId(payload.id);

			if (!localAppointment) {
				logger.info("Appointment not found locally, nothing to delete", {
					appointmentId: payload.id,
				});
				return;
			}

			// Delete from AutoPatient if exists
			if (localAppointment.apId) {
				await this.appointmentSyncService.deleteAppointmentFromAP(localAppointment);
			}

			// Mark as deleted locally (soft delete)
			await this.db
				.update(this.dbSchema.appointment)
				.set({
					ccData: null,
					ccUpdatedAt: new Date(),
				})
				.where(this.db.eq(this.dbSchema.appointment.id, localAppointment.id));

			logger.info("CC appointment deleted successfully", {
				appointmentId: payload.id,
				localId: localAppointment.id,
				apId: localAppointment.apId,
			});
		} catch (error) {
			logger.error("Failed to process CC appointment deleted", {
				appointmentId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle AutoPatient appointment created webhook
	 */
	async handleAPAppointmentCreated(payload: GetAPAppointmentType): Promise<void> {
		try {
			logger.info("Processing AP appointment created", {
				appointmentId: payload.id,
				contactId: payload.contactId,
				startTime: payload.startTime,
			});

			// Check if appointment already exists locally
			let localAppointment = await this.getAppointmentByAPId(payload.id);

			if (localAppointment?.ccId) {
				logger.info("Appointment already exists in CC, skipping creation", {
					appointmentId: payload.id,
					ccId: localAppointment.ccId,
				});
				return;
			}

			// Ensure patient exists locally and in CC
			const localPatient = await this.patientSyncService.findOrCreatePatientByAPId(payload.contactId);

			// Create or update local appointment
			if (!localAppointment) {
				await this.upsertLocalFromAP(payload, localPatient.id);
			} else {
				// Update existing appointment
				await this.db
					.update(this.dbSchema.appointment)
					.set({
						apData: payload,
						apUpdatedAt: new Date(payload.dateUpdated || new Date()),
						patientId: localPatient.id,
					})
					.where(this.db.eq(this.dbSchema.appointment.apId, payload.id));

				await this.refresh();
			}

			// Sync to CliniCore if this appointment wasn't originally from CC
			if (!this.appointment?.ccId) {
				await this.appointmentSyncService.syncAPAppointmentToCC(this.appointment!);
			}

			logger.info("AP appointment created and synced successfully", {
				appointmentId: payload.id,
				localId: this.appointment?.id,
				ccId: this.appointment?.ccId,
			});
		} catch (error) {
			logger.error("Failed to process AP appointment created", {
				appointmentId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}

	/**
	 * Handle AutoPatient appointment updated webhook
	 */
	async handleAPAppointmentUpdated(payload: GetAPAppointmentType): Promise<void> {
		try {
			logger.info("Processing AP appointment updated", {
				appointmentId: payload.id,
				contactId: payload.contactId,
				startTime: payload.startTime,
			});

			// Get existing appointment or create if not exists
			let localAppointment = await this.getAppointmentByAPId(payload.id);

			if (!localAppointment) {
				logger.info("Appointment not found locally, creating new", {
					appointmentId: payload.id,
				});
				
				const localPatient = await this.patientSyncService.findOrCreatePatientByAPId(payload.contactId);
				await this.upsertLocalFromAP(payload, localPatient.id);
			} else {
				// Update existing appointment
				await this.db
					.update(this.dbSchema.appointment)
					.set({
						apData: payload,
						apUpdatedAt: new Date(payload.dateUpdated || new Date()),
					})
					.where(this.db.eq(this.dbSchema.appointment.apId, payload.id));

				await this.refresh();
			}

			// Sync to CliniCore if appointment exists there
			if (this.appointment?.ccId) {
				await this.appointmentSyncService.syncAPAppointmentToCC(this.appointment);
			}

			logger.info("AP appointment updated and synced successfully", {
				appointmentId: payload.id,
				localId: this.appointment?.id,
				ccId: this.appointment?.ccId,
			});
		} catch (error) {
			logger.error("Failed to process AP appointment updated", {
				appointmentId: payload.id,
				error: error.message,
				stack: error.stack,
			});
			throw error;
		}
	}
}
