import { dbSchema, getDb } from "@database";
import type { GetAPAppointmentType } from "@libAP/APTypes";
import type { GetCCAppointmentType } from "@libCC/CCTypes";
import { eq, type SQLWrapper } from "drizzle-orm";
import { logger } from "@/utils";
import { format, parseISO } from "date-fns";

export type TLocalAppointment = typeof dbSchema.appointment.$inferSelect;

export class LocalAppointment {
	protected query = getDb().query.appointment;
	protected db = getDb();
	public appointment: TLocalAppointment | undefined;
	public ccAppointment: GetCCAppointmentType | undefined | null;
	public apAppointment: GetAPAppointmentType | undefined | null;
	protected log = logger;
	protected payload: GetCCAppointmentType | GetAPAppointmentType | null = null;
	protected dbSchema: typeof dbSchema;

	constructor() {
		this.query = getDb().query.appointment;
		this.db = getDb();
		this.dbSchema = dbSchema;
	}

	async getAppointmentById(id: string): Promise<TLocalAppointment | undefined> {
		this.appointment = await this.query.findFirst({
			where: eq(dbSchema.appointment.id, id),
		});
		this.ccAppointment = this.appointment?.ccData;
		this.apAppointment = this.appointment?.apData;
		return this.appointment;
	}

	async getAppointmentByCCId(id: number): Promise<TLocalAppointment | undefined> {
		this.appointment = await this.query.findFirst({
			where: eq(dbSchema.appointment.ccId, id),
		});
		this.ccAppointment = this.appointment?.ccData;
		this.apAppointment = this.appointment?.apData;
		return this.appointment;
	}

	async getAppointmentByAPId(id: string): Promise<TLocalAppointment | undefined> {
		this.appointment = await this.query.findFirst({
			where: eq(dbSchema.appointment.apId, id),
		});
		this.ccAppointment = this.appointment?.ccData;
		this.apAppointment = this.appointment?.apData;
		return this.appointment;
	}

	async getAppointmentsByPatientId(patientId: string): Promise<TLocalAppointment[]> {
		const appointments = await this.db.query.appointment.findMany({
			where: eq(dbSchema.appointment.patientId, patientId),
		});
		return appointments;
	}

	async upsertLocalFromCC(appointment: GetCCAppointmentType, patientId: string) {
		this.payload = appointment;
		this.appointment = await this.query.findFirst({
			where: eq(dbSchema.appointment.ccId, appointment.id),
		});

		if (this.appointment) {
			// Update existing appointment
			const updated = await this.db
				.update(dbSchema.appointment)
				.set({
					ccData: appointment,
					ccUpdatedAt: new Date(appointment.updatedAt || new Date()),
					patientId: patientId,
				})
				.where(eq(dbSchema.appointment.ccId, appointment.id))
				.returning();

			this.appointment = updated[0];
			this.ccAppointment = this.appointment.ccData;
			this.apAppointment = this.appointment.apData;
			return this.appointment;
		}

		// Create new appointment
		const startTime = appointment.startsAt ? parseISO(appointment.startsAt) : new Date();
		const endTime = appointment.endsAt ? parseISO(appointment.endsAt) : new Date();

		const create = await this.db
			.insert(dbSchema.appointment)
			.values({
				ccId: appointment.id,
				ccData: appointment,
				ccUpdatedAt: new Date(appointment.updatedAt || new Date()),
				patientId: patientId,
			})
			.returning();

		this.appointment = create[0];
		this.ccAppointment = this.appointment.ccData;
		this.apAppointment = this.appointment.apData;
		return this.appointment;
	}

	async upsertLocalFromAP(appointment: GetAPAppointmentType, patientId: string) {
		this.payload = appointment;
		this.appointment = await this.query.findFirst({
			where: eq(dbSchema.appointment.apId, appointment.id),
		});

		if (this.appointment) {
			// Update existing appointment
			const updated = await this.db
				.update(dbSchema.appointment)
				.set({
					apData: appointment,
					apUpdatedAt: new Date(appointment.dateUpdated || new Date()),
					patientId: patientId,
				})
				.where(eq(dbSchema.appointment.apId, appointment.id))
				.returning();

			this.appointment = updated[0];
			this.ccAppointment = this.appointment.ccData;
			this.apAppointment = this.appointment.apData;
			return this.appointment;
		}

		// Create new appointment
		const create = await this.db
			.insert(dbSchema.appointment)
			.values({
				apId: appointment.id,
				apData: appointment,
				apUpdatedAt: new Date(appointment.dateUpdated || new Date()),
				patientId: patientId,
			})
			.returning();

		this.appointment = create[0];
		this.ccAppointment = this.appointment.ccData;
		this.apAppointment = this.appointment.apData;
		return this.appointment;
	}

	async refresh(id?: string): Promise<TLocalAppointment | undefined> {
		if (id) {
			this.appointment = await this.query.findFirst({
				where: eq(dbSchema.appointment.id, id),
			});
			return this.appointment;
		}
		if (this.appointment) {
			this.appointment = await this.query.findFirst({
				where: eq(dbSchema.appointment.id, this.appointment.id),
			});
			this.ccAppointment = this.appointment?.ccData;
			this.apAppointment = this.appointment?.apData;
			return this.appointment;
		}
		this.log.debug("Appointment not found, while refreshing", {
			appointmentId: id,
			appointment: this.appointment,
		});
		throw new Error("Appointment not found");
	}

	async getPayload() {
		return this.payload;
	}

	async findBy(
		name: keyof typeof dbSchema.appointment.$inferSelect,
		value:
			| string
			| number
			| Date
			| GetAPAppointmentType
			| GetCCAppointmentType
			| SQLWrapper,
	) {
		return await this.query.findFirst({
			where: eq(dbSchema.appointment[name], value),
		});
	}

	/**
	 * Get appointment start time as Date object
	 */
	getStartTime(): Date | null {
		if (this.ccAppointment?.startsAt) {
			return parseISO(this.ccAppointment.startsAt);
		}
		if (this.apAppointment?.startTime) {
			return parseISO(this.apAppointment.startTime);
		}
		return null;
	}

	/**
	 * Get appointment end time as Date object
	 */
	getEndTime(): Date | null {
		if (this.ccAppointment?.endsAt) {
			return parseISO(this.ccAppointment.endsAt);
		}
		if (this.apAppointment?.endTime) {
			return parseISO(this.apAppointment.endTime);
		}
		return null;
	}

	/**
	 * Get appointment title/description
	 */
	getTitle(): string {
		if (this.ccAppointment?.title) {
			return this.ccAppointment.title;
		}
		if (this.apAppointment?.title) {
			return this.apAppointment.title;
		}
		return "No title";
	}

	/**
	 * Check if appointment is cancelled
	 */
	isCancelled(): boolean {
		if (this.ccAppointment?.canceledAt) {
			return true;
		}
		if (this.apAppointment?.appointmentStatus === "cancelled") {
			return true;
		}
		return false;
	}

	/**
	 * Format appointment time for display
	 */
	formatTime(time: Date | null, formatString = "yyyy-MM-dd HH:mm"): string {
		if (!time) return "Unknown";
		return format(time, formatString);
	}
}
