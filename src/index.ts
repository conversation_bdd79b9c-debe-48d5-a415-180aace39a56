import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { logger } from "@/utils";
import { webhookRoutes } from "./webhooks";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
  logger.error("Application error", {
    error: err.message,
    stack: err.stack,
    path: c.req.path,
    method: c.req.method,
  });
  return c.json(
    {
      message: "Internal Server Error",
      requestId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
    },
    500
  );
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) =>
  c.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
  })
);

// Webhook routes
app.route("/webhooks", webhookRoutes);

export default app;
