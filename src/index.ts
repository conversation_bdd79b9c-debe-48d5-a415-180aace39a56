import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { logger } from "@/utils";
import { webhookRoutes } from "./webhooks";
import { monitoringService } from "./services/MonitoringService";
import { errorHandler } from "./services/ErrorHandlingService";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
  logger.error("Application error", {
    error: err.message,
    stack: err.stack,
    path: c.req.path,
    method: c.req.method,
  });
  return c.json(
    {
      message: "Internal Server Error",
      requestId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
    },
    500
  );
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", async (c) => {
  try {
    const health = await monitoringService.getSystemHealth();
    return c.json(health);
  } catch (error) {
    logger.error("Health check failed", {
      error: error instanceof Error ? error.message : String(error),
    });
    return c.json(
      {
        overall: "unhealthy",
        timestamp: new Date().toISOString(),
        error: "Health check failed",
      },
      503
    );
  }
});

app.get("/health/simple", (c) =>
  c.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
  })
);

app.get("/metrics", async (c) => {
  try {
    const stats = await errorHandler.getErrorStats();
    const health = await monitoringService.getSystemHealth();

    return c.json({
      timestamp: new Date().toISOString(),
      uptime: health.metrics.uptime,
      memory: health.metrics.memoryUsage,
      errors: stats,
      sync: health.metrics.syncStats,
    });
  } catch (error) {
    logger.error("Metrics endpoint failed", {
      error: error instanceof Error ? error.message : String(error),
    });
    return c.json({ error: "Metrics unavailable" }, 500);
  }
});

// Webhook routes
app.route("/webhooks", webhookRoutes);

export default app;
